# Rewards Management System

## Overview

The Rewards Management System is a comprehensive loyalty framework within the Perkd application that enables merchants to create structured reward programs with progressive levels, customizable stamps, and completion incentives. The system provides flexible loyalty program design through a hierarchical data model that supports multi-level engagement programs, point collections, and stamp cards.

This document provides detailed technical analysis of the rewards system architecture, focusing on user experience flows and data flow patterns within the broader application architecture. For general application architecture patterns, see [Application Architecture](app-architecture.md).

## Technology Stack

### Core Components
- **Data Layer**: Realm database with hierarchical reward models
- **Business Logic**: Centralized reward services and utilities
- **UI Components**: React Native components with ViewPager navigation
- **State Management**: Event-driven architecture with real-time updates
- **Integration Layer**: REST API services for external reward operations

### Key Dependencies
- **Navigation**: React Native Navigation with modal and stack support
- **Database**: Realm Object Database for local reward data persistence
- **Synchronization**: Background sync with conflict resolution
- **Notifications**: Firebase push notifications for reward events
- **Analytics**: Event tracking for reward interactions and completions

## Architecture Overview

The Rewards Management System follows a layered architecture that integrates seamlessly with the broader Perkd application architecture:

```mermaid
graph TB
    subgraph "🎁 Rewards Management System"
        subgraph "📱 Presentation Layer"
            RUI[Reward UI Components<br/>RewardItem, RewardList]
            RScreen[Reward Screens<br/>Rewards.js, List.js]
            RWidget[Reward Widget<br/>DataWidget Extension]
        end

        subgraph "🎯 Business Logic Layer"
            RController[Reward Controller<br/>Navigation & State]
            RService[Reward Services<br/>API Integration]
            RUtils[Reward Utilities<br/>Business Logic]
        end

        subgraph "💾 Data Layer"
            RModel[Reward Models<br/>Hierarchical Structure]
            RSync[Reward Sync<br/>Background Updates]
            RDB[(Realm Database<br/>Local Storage)]
        end
    end

    subgraph "🔗 Integration Points"
        CardSys[Card System<br/>Card Association]
        ShopSys[Shopping System<br/>Purchase Triggers]
        NotifySys[Notification System<br/>Event Alerts]
        OfferSys[Offer System<br/>Completion Rewards]
    end

    %% Internal connections
    RScreen --> RController
    RUI --> RScreen
    RWidget --> RController
    RController --> RService
    RController --> RUtils
    RService --> RModel
    RUtils --> RModel
    RModel --> RSync
    RSync --> RDB

    %% External integrations
    CardSys --> RWidget
    ShopSys --> RUtils
    RUtils --> NotifySys
    RUtils --> OfferSys

    %% Styling with darker backgrounds and white text
    classDef presentation fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef business fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef data fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef integration fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class RUI,RScreen,RWidget presentation
    class RController,RService,RUtils business
    class RModel,RSync,RDB data
    class CardSys,ShopSys,NotifySys,OfferSys integration
```

## Data Model Architecture

### Hierarchical Data Structure

The rewards system implements a sophisticated hierarchical data model that enables flexible loyalty program design:

#### Core Models

**Reward Model** (`src/lib/models/Reward.js`):
- Primary container for loyalty programs
- Associated with specific cards through `cardId`
- Contains multiple levels and overall program constraints
- Manages state transitions and completion tracking

**RewardLevel Model** (`src/lib/models/Reward/Level.js`):
- Represents tiers or stages within a reward program
- Contains collections of stamps and level-specific branding
- Tracks completion status and offer issuance independently
- Supports progressive level unlocking

**Stamp Model** (`src/lib/models/Reward/Stamp.js`):
- Individual stamp entities within levels
- Tracks stamped status and issuance timestamps
- Supports custom stamp and background images
- Enables granular progress tracking

#### Database Schema

The Realm database schema defines the complete reward data structure:

````javascript
class Reward extends Realm.Object {}
Reward.schema = {
    name: 'Reward',
    primaryKey: 'id',
    properties: {
        id: 'string',
        masterId: 'string',
        name: { type: 'string?', default: '' },
        brand: { type: 'string?', default: '' },
        kind: 'string?',
        startTime: 'date?',
        endTime: 'date?',
        activeTime: 'date?',  // Alternative activation time for state transitions
        purgeTime: 'date?',   // Automatic cleanup time for expired rewards
        logoImage: 'RewardLogo',
        levels: { type: 'RewardLevel[]', default: [] },
        transactions: { type: 'RewardTransaction[]', default: [] },
        options: 'RewardOptions',
        channels: { type: 'string?[]', default: [] },
        state: { type: 'string?', default: 'pending' },
        when: 'RewardWhen',  // Timestamps for lifecycle events (received, completed, etc.)
        globalize: 'RewardGlobalize',
        // ... additional properties
    }
};
````

#### State Management

Rewards implement comprehensive state management with clear behavioral definitions:

| State | Description | Behavior | Query Conditions |
|-------|-------------|----------|------------------|
| **active** | Program is currently running | Stamps can be collected, full functionality available | `notDeleted && notCancelled && notPending && notPurged` |
| **completed** | All stamps are collected | Eligible for redemption, progression complete | All stamps marked as `stamped: true` |
| **expired** | Past end date | No longer collectable, read-only access | `endTime < NOW` |
| **notStarted** | Before start date | Visible but inactive, preview mode | `startTime > NOW` |
| **pending** | Issued but not yet active | Awaiting activation, limited visibility | `state == "pending"` |
| **cancelled** | Program cancelled | No longer available, hidden from UI | `state == "cancelled"` |

**Note**: The above states apply to reward-level management. Level-specific states are managed separately using `LEVEL_STATE` constants and include an additional `fullyRedeemed` state for individual levels.

#### Advanced State Management Features

#### Level-Specific State Management

**Level States** (using `LEVEL_STATE` constants):
- **ACTIVE**: Level is current and accepting stamps (default state when conditions met)
- **COMPLETED**: All stamps in level completed (`Rewards.completedLevel(level) == true`)
- **EXPIRED**: Level past end date (`endTime < NOW`)
- **NOT_STARTED**: Level not yet available (`startTime > NOW`)
- **FULLREDEEMED**: Level completed and all offers redeemed (`fullyRedeemedAt && offerIds.length === 0`)

**State Constants Implementation**:
````javascript
// Reward-level states (src/lib/rewards.js)
export const STATE = {
    PENDING: 'pending',
    ACTIVE: 'active',
    CANCELLED: 'cancelled',
    NOT_STARTED: 'notStarted',
    EXPIRED: 'expired',
    COMPLETED: 'completed',
};

// Level-specific states (src/lib/rewards.js)
export const LEVEL_STATE = {
    NOT_STARTED: 'notStarted',
    ACTIVE: 'active',
    EXPIRED: 'expired',
    COMPLETED: 'completed',
    FULLREDEEMED: 'fullyRedeemed',
};

// Priority constants for sorting and display order (src/lib/rewards.js)
export const PRIORITY = {
    active: 1,
    notStarted: 2,
    shared: 3,
    fullyRedeemed: 4,
    redeemed: 5,
    completed: 6,
    cancelled: 7,
    expired: 8,
    archived: 9,
};
````

**Automatic State Transitions**:
- **Pending to Active**: Rewards automatically transition from PENDING to ACTIVE when `startTime` or `activeTime` is reached
- **Purge Management**: Expired rewards are automatically purged when `purgeTime < NOW`
- **Lifecycle Hooks**: `afterPersist` hook automatically sets `when.received` timestamp and emits events

**Query Optimization Patterns**:
- **Engagement Query**: `notDeleted && notPurged && notExpired && issued && state = "active"` for engagement system
- **Active Query**: `notDeleted && notCancelled && notPending && notPurged` for general active rewards
- **Performance Optimization**: Queries include performance benchmarks (e.g., ~2.61ms on iPad Mini 2)

Each level maintains independent state management, allowing for multi-phase programs with distinct timelines and progression rules.

### Data Relationships

The reward system maintains complex relationships across multiple entities:

- **Card-Reward Association**: Each reward is linked to a specific card via `cardId`
- **Level Hierarchy**: Rewards contain ordered levels with progressive unlocking
- **Stamp Collections**: Each level contains multiple stamps for progress tracking
- **Transaction History**: Complete audit trail of stamp issuance and deductions
- **Offer Integration**: Completed levels can trigger automatic offer issuance

## User Experience Flow

### Reward Discovery and Engagement

The user experience flow emphasizes contextual discovery and progressive engagement:

```mermaid
graph TB
    subgraph "🚀 Entry Points"
        CardView[Card View<br/>Widget Display]
        PushNotif[Push Notification<br/>New Stamps]
        ShopFlow[Shopping Flow<br/>Purchase Triggers]
        DeepLink[Deep Link<br/>External Access]
    end

    subgraph "🎯 Core Experience"
        RewardList[Reward List<br/>Multiple Programs]
        RewardDetail[Reward Detail<br/>Level Navigation]
        StampView[Stamp Progress<br/>Visual Tracking]
        LevelComplete[Level Completion<br/>Achievement]
    end

    subgraph "🎁 Completion Flow"
        OfferIssue[Offer Issuance<br/>Automatic Rewards]
        Notification[Success Notification<br/>User Alert]
        NextLevel[Next Level<br/>Progression]
        ShareReward[Share Achievement<br/>Social Integration]
    end

    subgraph "🔄 Engagement Loop"
        PurchaseTrigger[Purchase Activity<br/>Stamp Earning]
        ProgressUpdate[Progress Update<br/>Real-time Sync]
        ContextualPrompt[Contextual Prompts<br/>Engagement Engine]
        RetentionFlow[Retention Flow<br/>Continued Engagement]
    end

    %% Entry flow
    CardView --> RewardList
    CardView --> RewardDetail
    PushNotif --> RewardDetail
    ShopFlow --> StampView
    DeepLink --> RewardDetail

    %% Core experience flow
    RewardList --> RewardDetail
    RewardDetail --> StampView
    StampView --> LevelComplete

    %% Completion flow
    LevelComplete --> OfferIssue
    LevelComplete --> Notification
    LevelComplete --> NextLevel
    OfferIssue --> ShareReward

    %% Engagement loop
    PurchaseTrigger --> ProgressUpdate
    ProgressUpdate --> StampView
    StampView --> ContextualPrompt
    ContextualPrompt --> RetentionFlow
    RetentionFlow --> PurchaseTrigger

    %% Styling with darker backgrounds and white text
    classDef entry fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef core fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef completion fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef engagement fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class CardView,PushNotif,ShopFlow,DeepLink entry
    class RewardList,RewardDetail,StampView,LevelComplete core
    class OfferIssue,Notification,NextLevel,ShareReward completion
    class PurchaseTrigger,ProgressUpdate,ContextualPrompt,RetentionFlow engagement
```

### Navigation Patterns

The rewards system implements sophisticated navigation patterns optimized for user engagement:

#### Multi-Level Navigation
- **Reward List**: Sectioned list with state-based grouping (Active, Completed, Expired)
- **Reward Detail**: ViewPager-based level navigation with smooth transitions
- **Level Focus**: Automatic focus on current active level with manual navigation
- **Deep Linking**: Direct navigation to specific rewards and levels

#### Controller Navigation Management

The Reward Controller manages complex navigation patterns:

**Core Navigation Methods**:
- **refresh(rewards)**: Updates reward data and notifies listeners of changes
- **getReward(id)**: Retrieves specific reward by ID for targeted navigation
- **view(callbacks, componentId, ...)**: Renders reward view with appropriate navigation context
- **notified(reward)**: Marks rewards as viewed and manages notification state

**Navigation Control Patterns**:
- **Level Navigation**: Handles smooth transitions between different reward levels with state preservation
- **Modal Presentations**: Displays reward details as modal overlays when appropriate for user context
- **Return Navigation**: Manages back navigation after viewing reward details with proper cleanup

#### State-Aware UI
- **Dynamic Topbar**: Context-aware pagination and navigation controls
- **Progress Indicators**: Visual progress tracking across levels and stamps
- **Status Tags**: Clear visual indicators for reward and level states
- **Unread Badges**: Notification badges for new stamps and completions

#### Visual Elements and Interactions

**Core Visual Components**:
- **Stamp Cards**: Digital representation of traditional loyalty stamp cards with authentic feel
- **Progress Bars**: Visual indicators for level completion with animated progress updates
- **Status Tags**: Clear indicators for completed, expired, or active states with color coding
- **Reward Images**: Custom imagery for stamps and levels with optimized loading

**Animation and Feedback**:
- **Stamp Collection**: Smooth animations for newly collected stamps with celebration effects
- **Level Completion**: Visual celebration sequences when levels are completed
- **Status Changes**: Transition effects when reward states change with clear visual feedback
- **Interactive Elements**: Touch feedback and hover states for enhanced user experience

## Data Flow Architecture

### Stamp Issuance Flow

The stamp issuance process involves multiple system components working in coordination:

```mermaid
sequenceDiagram
    participant User
    participant Shop as Shopping System
    participant Bag as Bag Controller
    participant Reward as Reward System
    participant Sync as Sync Engine
    participant Backend as Backend API
    participant Notify as Notification System

    User->>Shop: Complete Purchase
    Shop->>Bag: Process Transaction
    Bag->>Bag: Calculate Qualifiers
    Bag->>Reward: Check Stamp Eligibility
    Reward->>Reward: Validate Qualifiers
    Reward->>Reward: Issue Stamps Locally
    Reward->>Sync: Queue Sync Update
    Sync->>Backend: Sync Stamp Transaction
    Backend-->>Sync: Confirm Transaction
    Sync->>Reward: Update Local State
    Reward->>Notify: Trigger Notification
    Notify->>User: Display Stamp Notification
    User->>Reward: View Stamp Progress
    Reward->>Reward: Mark as Notified
```

### Level Completion Flow

Level completion triggers a cascade of events across multiple systems:

1. **Completion Detection**: Automatic detection when all stamps in a level are completed
2. **State Updates**: Level marked as completed with timestamp
3. **Offer Issuance**: Automatic offer creation for completed levels (if configured)
4. **Notification Dispatch**: User notification for level completion
5. **Progress Advancement**: Automatic progression to next level (if available)
6. **Analytics Tracking**: Event logging for completion analytics

### Synchronization Patterns

The rewards system implements sophisticated synchronization patterns:

- **Optimistic Updates**: Local updates applied immediately for responsive UI
- **Background Sync**: Periodic synchronization with conflict resolution
- **Event-Driven Updates**: Real-time UI updates through event system
- **Offline Support**: Queue-based operations for offline scenarios

## Business Logic Implementation

### Reward State Management

The system implements comprehensive state management for rewards and levels:

````javascript
// REWARDS_QUERY definition
const REWARDS_QUERY = 'deletedAt == null && state != "pending" && state != "cancelled" && (purgeTime == null || purgeTime > $1)';

export const Rewards = {
    // Get rewards by card ID with filtering
    getByCardId: (cardId, options = {}) => {
        const query = options.all ? 'cardId == $0' : `cardId == $0 && ${REWARDS_QUERY}`;
        return _.Reward.find(query, cardId, newDate()).sorted('modifiedAt', true);
    },

    // Calculate stamp status across all levels
    stampsStatus: (reward) => {
        const status = { total: 0, outstanding: 0 };
        if (reward?.levels) {
            reward.levels.forEach(level => {
                if (level.stamps) {
                    status.total += level.stamps.length;
                    level.stamps.forEach(stamp => {
                        if (!stamp.stamped) status.outstanding += 1;
                    });
                }
            });
        }
        return status;
    },

    // Check if reward is completed
    completed: (reward) => {
        const { total, outstanding } = Rewards.stampsStatus(reward);
        return total && (outstanding === 0);
    }
};
````

### Qualification System

The rewards system implements sophisticated qualification logic for stamp issuance:

#### Stamp Collection Mechanisms

Stamps are collected through various mechanisms designed for flexible loyalty program design:

- **Purchase-based**: Stamps awarded based on transaction criteria and spending thresholds
- **Visit-based**: Stamps awarded for store visits and location-based interactions
- **Action-based**: Stamps awarded for specific user actions and engagement activities
- **Manual-issue**: Stamps issued directly by merchants for special circumstances

#### Purchase Qualifiers

**Advanced Qualifier System**:
- **Minimum Spend**: Configurable via `qualifiers.issuestamps.totalPrice.$gte` or `$gt`
- **Product Categories**: Category-specific rules using `qualifiers.issuestamps.filter` with sift.js evaluation
- **Channel Restrictions**: Platform-specific issuance via `channels` array (e.g., "perkd", "instore")
- **Time-Based Rules**: Date/time restrictions through `startTime` and `endTime` validation
- **Daily Limits**: Optional daily stamp limits via `qualifiers.issuestamps.options.dailyStampLimit`

**Qualifier Evaluation Logic**:
- **Real-time Validation**: Purchase validation using `sift(qualifier, SIFT_OPTIONS)(combinedContext)`
- **Filter Application**: Item filtering before price calculation when `filter` is specified
- **Spend Calculation**: `spendPerStamp = qualifiers.issuestamps.totalPrice.$gte || $gt`
- **Fraud Prevention**: Multiple validation layers including channel verification and timing constraints

#### Transaction System

Stamp collection is tracked through a comprehensive transaction system with detailed audit capabilities:

**Transaction Types**:
- **Issue**: Records addition of stamps with metadata (`type: "issue"`, `quantity`, `level`, `createdAt`)
- **Deduct**: Tracks removal of stamps for refunds/corrections (`type: "deduct"`)
- **Carryover**: Manages transfer of excess stamps between levels (`type: "carryover"`)
- **Notification Tracking**: `notifiedAt` timestamp tracks user notification status

**Advanced Transaction Features**:
- **Qualifier Parsing**: JSON parsing of MongoDB qualifiers with error handling in `beforePersist`
- **Batch Processing**: Transaction batching for performance optimization
- **Audit Trail**: Complete transaction history with timestamps and metadata
- **Error Recovery**: Deferred action system for failed notification attempts

#### Validation Logic
- **Real-time Validation**: Purchase validation against reward qualifiers
- **Fraud Prevention**: Multiple validation layers to prevent abuse
- **Business Rules**: Merchant-specific rules and constraints
- **Audit Trail**: Complete transaction history for compliance

#### Level Structure and Progression

**Multi-Level Architecture**:
- **Multiple Levels**: Programs can have multiple sequential or parallel tiers for complex loyalty structures
- **Progressive Difficulty**: Later levels can require more stamps or have different qualification rules
- **Independent Timelines**: Each level can have its own start and end dates for flexible program management
- **Custom Visuals**: Levels can have unique branding and messaging for enhanced user experience

**Level Completion Detection**:
- **Completion Algorithm**: `stamps.length === stamps.filter(s => s.stamped).length`
- **Current Level Calculation**: `atLevel` getter finds highest completed level or level with at least one stamp
- **State Evaluation**: Complex state evaluation considering `fullyRedeemedAt`, `offerIds`, and timing constraints

**Completion and Rewards**:
- **Automatic Offer Issuance**: Completed levels trigger offer creation with `offerIds` tracking
- **Status Update**: Level `completedAt` timestamp and reward status updated automatically
- **Next Level Activation**: Progressive unlocking based on completion status
- **Achievement Tracking**: `when.completed` timestamp for reward completion events

## Integration Architecture

### Card System Integration

The rewards system integrates deeply with the card management system:

#### Widget Integration

The Reward Widget extends the DataWidget framework and serves as the entry point for reward functionality within cards:

````javascript
export default class Reward extends DataWidget {
    constructor(definition, owner, data = {}, credentials) {
        super(definition, owner, data, credentials);

        const { param } = this,
            exclude = this.master.widgets
                .filter(w => w.param.model === REWARD)
                .reduce((kinds, w) => kinds.concat(w.param.kinds), []);

        if (!param.kinds && !!exclude.length) {
            const { id: cardId } = this.card,
                kinds = _.Reward.kinds(cardId).filter(k => !exclude.includes(k));
            Object.assign(param, { kinds: kinds.length ? kinds : [ REWARD ] });
        }
    }
}
````

#### Widget Architecture and Lifecycle

**Base Architecture**:
- **DataWidget Extension**: Leverages data management capabilities from the base DataWidget class
- **Controller Integration**: Initializes and communicates with the Reward Controller for business logic
- **Event Management**: Manages lifecycle events for reward updates through the centralized Event system

**Configuration Parameters**:
- **Model**: Specifies the data model (default: 'reward')
- **Kinds**: Defines reward types to display, handling different reward categories
- **Filtering**: Automatically excludes kinds handled by other widgets on the same card

**Lifecycle Methods**:
- **Constructor**: Sets up initial configuration and determines reward kinds to display based on card capabilities
- **init()**: Initializes the widget and sets the Controller reference for business logic delegation
- **addListeners()**: Registers for reward update and deletion events through the event system
- **removeListeners()**: Cleans up event listeners when the widget is no longer needed to prevent memory leaks

#### Card-Reward Lifecycle
- **Automatic Association**: Rewards automatically associated with cards upon issuance
- **Widget Configuration**: Dynamic widget configuration based on card capabilities
- **State Synchronization**: Card state changes reflected in reward availability
- **Cleanup Operations**: Automatic cleanup when cards are deleted or expired

### Shopping System Integration

The rewards system integrates with the shopping and checkout systems for stamp issuance:

#### Purchase Integration

````javascript
static reward(cardId, limit = {}) {
    const [ reward ] = !limit.noRewards
        ? _.Reward.findByCardId(cardId)
            .filter(r =>
                r.qualifiers?.issuestamps
            && (r.endTime ? (new Date(r.endTime) > newDate()) : true)
            && (!r.channels?.length || r.channels.includes(PERKD)))
        : [];
    return reward;
}
````

#### Upsell Integration
- **Dynamic Upsell Calculation**: Real-time calculation of spend needed for next stamp
- **Visual Progress Indicators**: Shopping cart integration with stamp progress
- **Contextual Messaging**: Personalized upsell messages based on reward progress
- **Cross-Sell Opportunities**: Product recommendations to achieve stamp thresholds

### Notification System Integration

The rewards system leverages the comprehensive notification system for user engagement through sophisticated engagement patterns:

#### Notification Types
- **Stamp Earned**: Immediate notification when stamps are issued via engagement system
- **Level Completed**: Achievement notifications for level completion
- **Reward Expiring**: Reminder notifications for expiring rewards
- **New Rewards**: Notifications for newly available reward programs

#### Advanced Engagement Integration

**PBox (Promotion Box) System**:
- **Stamp PBox**: Rich visual overlays for new stamp notifications with brand imagery
- **Transaction-Based**: Notifications tied to specific transaction IDs for precise tracking
- **Avatar Integration**: Brand logos or sharer images for personalized notifications
- **Quantity Display**: Shows exact number of stamps earned with localized messaging

**Engagement Query Optimization**:
- **Performance Benchmarks**: Engagement queries optimized with documented performance metrics
- **Active Card Validation**: Ensures notifications only for active cards via `findOneActiveById`
- **Transaction Filtering**: Finds unnotified transactions using `!tx.notifiedAt` filter
- **LIFO Processing**: Last-in-first-out processing for engagement queue management

#### Engagement Integration

````javascript
StampPBox = {
    doNavTo(data, transaction) {
        const engagingId = getEngagingId(data);
        if (!isValid(data)) return rejectInvalidData(engagingId, 'navTo');

        const instance = isArray(data) ? data[0] : data,
            { cardId, _model } = instance,
            model = _model.toLowerCase(),
            insRoute = {};

        insRoute[model] = { id: engagingId };
        Object.assign(insRoute[model], {
            level: transaction.level,
            transactionId: transaction.id,
        });

        return Promise.all([
            navTo([{ card: { id: cardId } }, insRoute ]),
            dismissPBox(data),
        ])
            .then(() => instance.notifyStamp(transaction.id, true))
            .then(() => resolveEngage(engagingId, 'navTo'));
    }
}
````

### Offer System Integration

Completed reward levels can automatically trigger offer issuance:

#### Automatic Offer Creation
- **Level Completion Triggers**: Offers automatically created when levels are completed
- **Offer ID Tracking**: Completed levels track associated offer IDs
- **Redemption Coordination**: Coordination between reward completion and offer redemption
- **Business Rule Engine**: Configurable rules for offer issuance

## API Endpoints and Services

### Reward Service Layer

The reward service layer provides comprehensive API integration:

````javascript
export default {
    fetch: ({ ids }) =>
        call({
            ...API.fetch,
            qs: { ids },
        }),

    notifyStamps: (id, transactionId, view, at) =>
        call({
            ...API.notifiedStamps,
            body: { transactionId, view, at },
            routeParams: { id },
        }),

    request: (id, cardId, rewardId, quantity = 1) =>
        call({
            ...API.request,
            routeParams: { id },
            body: { cardId, rewardId, quantity },
        }),
};
````

### Action Layer

The action layer provides high-level operations for reward management:

````javascript
export const Actions = {
    request: (data = {}) => {
        const { masterId, cardId, id, quantity } = data;
        return (masterId && cardId && id) ? RewardService.request(masterId, cardId, id, quantity) : Promise.reject();
    },
};
````

### API Integration Patterns

- **RESTful Endpoints**: Standard REST API patterns for reward operations
- **Batch Operations**: Efficient batch processing for multiple reward operations
- **Error Handling**: Comprehensive error handling with retry mechanisms
- **Caching Strategy**: Multi-level caching for performance optimization

## Advanced System Integration

### Engagement System Architecture

The rewards system implements sophisticated integration with the engagement system for contextual user notifications:

#### Engagement Query Patterns

**Performance-Optimized Queries**:
- **Specific Engagement**: `Reward.engage({ id, level, transactionId })` with documented 13.56ms performance on iPad Mini 2
- **Next Engagement**: `Reward.nextEngage(cardId)` with LIFO processing and card validation
- **Active Reward Filtering**: Uses `QUERY.engage` for optimal performance with complex conditions

**Engagement Data Flow**:
1. **Transaction Creation**: Stamp transactions created with unique IDs and metadata
2. **Engagement Detection**: System scans for unnotified transactions using `!tx.notifiedAt`
3. **Card Validation**: Ensures engagement only for active cards via `_.Card.findOneActiveById`
4. **PBox Generation**: Creates rich notification data with brand imagery and localized messaging
5. **Notification Tracking**: Updates `notifiedAt` timestamp after successful notification

#### Advanced Engagement Features

**PBox (Promotion Box) Integration**:
- **Rich Visual Data**: Includes brand logos, stamp images, and personalized messaging
- **Transaction Linking**: Direct linking between notifications and specific transactions
- **Quantity Display**: Shows exact stamp count with localized pluralization
- **Avatar System**: Supports both brand logos and sharer profile images
- **Action Buttons**: Configurable action buttons for view/dismiss functionality

**Engagement Priority System**:
- **LIFO Processing**: Last-in-first-out processing ensures most recent rewards get priority
- **Card-Based Grouping**: Groups rewards by card for efficient processing
- **Active Card Filtering**: Only processes rewards for currently active cards
- **Performance Monitoring**: Built-in performance benchmarks for optimization

### Lifecycle Management and Automation

#### Automatic State Transitions

**Reward Lifecycle Automation**:
- **Pending to Active**: `refreshRewardsState()` automatically transitions rewards when timing conditions are met using query `deletedAt == null && (startTime < NOW || activeTime < NOW) && state == "pending"`
- **Purge Management**: Automatic cleanup of expired rewards using `purgeTime < NOW` queries
- **Received Timestamp**: `afterPersist` hook automatically sets `when.received` for new rewards
- **Event Emission**: Automatic event emission for reward lifecycle changes

**Transaction Processing Automation**:
- **Qualifier Parsing**: Automatic JSON parsing of MongoDB qualifiers with error handling
- **Batch Updates**: Efficient batch processing for multiple reward state changes
- **Deferred Actions**: Failed operations automatically queued for retry via deferred action system
- **Audit Trail**: Complete transaction history maintained automatically

#### Advanced Business Logic

**Level Progression Algorithms**:
- **Current Level Detection**: `atLevel` getter uses reverse iteration for optimal performance
- **Completion Detection**: Sophisticated completion logic considering all stamps in all levels
- **Image Management**: Dynamic image selection based on current level and completion status
- **Offer Integration**: Automatic offer ID tracking for completed levels

**Qualification and Validation**:
- **Real-time Qualification**: Purchase qualification using sift.js for complex rule evaluation
- **Filter Application**: Optional item filtering before price calculation for targeted rewards
- **Channel Validation**: Multi-channel support with platform-specific validation
- **Daily Limits**: Optional daily stamp limits with sophisticated tracking

## Performance Optimization

### Rendering Optimization

The rewards system implements sophisticated rendering optimizations:

#### ViewPager Optimization
- **Lazy Rendering**: Only render visible and adjacent reward levels
- **Memory Management**: Automatic cleanup of off-screen components
- **Image Optimization**: Cached image loading with placeholder support
- **Animation Performance**: Hardware-accelerated animations for smooth transitions

#### List Performance
- **Sectioned Lists**: Efficient sectioned list rendering for reward lists
- **Virtual Scrolling**: Virtual scrolling for large reward collections
- **State-Based Filtering**: Efficient filtering based on reward states
- **Memoization**: Component memoization for expensive renders

#### Advanced Performance Optimizations

**Data Management Optimizations**:
- **Selective Loading**: Only loads active rewards to reduce memory usage and improve startup time
- **Transaction Batching**: Groups stamp operations to minimize database access and improve throughput
- **State Caching**: Caches reward states to avoid recalculation and improve response times
- **Render Range Limitation**: Only renders reward content within the visible range for memory efficiency

**UI Performance Enhancements**:
- **Virtualized Level Display**: Uses efficient rendering for rewards with many levels
- **Image Optimization**: Optimizes stamp and reward imagery with progressive loading
- **Animation Control**: Carefully manages animations to prevent performance issues on lower-end devices
- **Component Recycling**: Reuses components where possible to reduce memory allocation

### Data Access Optimization

- **Query Optimization**: Optimized Realm queries with proper indexing
- **Lazy Loading**: Lazy loading of reward details and images
- **Prefetching**: Intelligent prefetching of likely-needed data
- **Background Processing**: Background processing for non-critical operations

## Security and Validation

### Data Validation

The rewards system implements comprehensive validation:

- **Input Validation**: Strict validation of all user inputs and API responses
- **Business Rule Validation**: Validation against merchant-specific business rules
- **Fraud Prevention**: Multiple layers of fraud prevention and detection
- **Audit Logging**: Comprehensive audit logging for compliance and debugging

### Security Measures

- **Secure Storage**: Sensitive reward data stored using secure storage mechanisms
- **API Security**: Secure API communication with proper authentication
- **Data Encryption**: Encryption of sensitive reward and transaction data
- **Access Control**: Role-based access control for reward management operations

## Error Handling and Recovery

### Error Classification

The rewards system implements sophisticated error handling across multiple layers:

**Network and API Errors**:
- **Connection Failures**: API call failures handled with deferred action system
- **Service Timeouts**: Timeout handling for reward service calls
- **Backend Sync Errors**: Sync failures queued for retry via `_.Action.defer`

**Validation and Business Logic Errors**:
- **Qualifier Parsing**: JSON parsing errors in `beforePersist` with error logging
- **Invalid Transactions**: Transaction validation with error tracking
- **State Transition Errors**: Invalid state changes prevented with validation
- **Engagement Errors**: Invalid object/transaction errors in engagement system

**System and Performance Errors**:
- **Query Performance**: Performance monitoring with documented benchmarks
- **Memory Management**: Automatic cleanup and resource management
- **Database Constraints**: Realm constraint violations handled gracefully

### Advanced Recovery Strategies

**Deferred Action System**:
- **Failed Notifications**: `Stamps.notify` failures automatically deferred via `_.Action.defer(REWARDS_LIB, 'notifyStamp', params, err)`
- **Retry Mechanism**: Automatic retry of failed operations through deferred action queue
- **Error Context**: Complete error context preserved for debugging and recovery

**State Recovery Mechanisms**:
- **Automatic State Refresh**: `refreshRewardsState()` automatically transitions pending rewards to active when `startTime` or `activeTime` conditions are met
- **Purge Management**: Automatic cleanup of expired rewards via `purgeTime` validation
- **Transaction Integrity**: Transaction rollback and recovery for failed operations

**Graceful Degradation Patterns**:
- **Offline Operation**: Local state management continues when network unavailable
- **Partial Functionality**: Core features remain available during service degradation
- **User Experience Continuity**: UI remains responsive with cached data during errors

## Testing and Quality Assurance

### Testing Strategy

- **Unit Testing**: Comprehensive unit testing for business logic components
- **Integration Testing**: Integration testing for system interactions
- **UI Testing**: Automated UI testing for user experience validation
- **Performance Testing**: Performance testing for optimization validation

### Quality Metrics

- **Code Coverage**: High code coverage for critical reward system components
- **Performance Metrics**: Response time and memory usage monitoring
- **User Experience Metrics**: User engagement and completion rate tracking
- **Error Rate Monitoring**: Comprehensive error rate monitoring and alerting

## Future Enhancements

### Planned Features

- **Advanced Analytics**: Enhanced analytics and reporting capabilities
- **Machine Learning**: ML-powered personalization and recommendation engine
- **Social Features**: Enhanced social sharing and referral capabilities
- **Gamification**: Advanced gamification features and achievement systems

### Scalability Considerations

- **Horizontal Scaling**: Architecture designed for horizontal scaling
- **Performance Optimization**: Continuous performance optimization and monitoring
- **Data Architecture**: Scalable data architecture for growing user base
- **Integration Flexibility**: Flexible integration architecture for new features

## Summary

The Rewards Management System represents a sophisticated loyalty platform that seamlessly integrates with the broader Perkd application architecture. The system emphasizes:

- **User-Centric Design**: Intuitive user experience with progressive engagement
- **Flexible Architecture**: Modular design supporting diverse loyalty program types
- **Performance Optimization**: Multi-level optimization for responsive user experience
- **Comprehensive Integration**: Deep integration with card, shopping, and notification systems
- **Scalable Foundation**: Architecture designed for growth and future enhancements

This architecture provides a robust foundation for complex loyalty programs while maintaining the flexibility needed for diverse merchant requirements and future business growth.

## Implementation Guidelines

### Development Patterns

When extending or modifying the rewards system, developers should follow these established patterns:

#### Architecture Patterns

**Core Architectural Principles**:
- **Widget Extension**: Extend the base Reward Widget for specialized functionality rather than creating standalone components
- **Controller Delegation**: Delegate business logic to the Reward Controller for proper separation of concerns
- **Model-based Calculations**: Use model methods for state and status calculations to ensure consistency
- **Event-Driven Updates**: Use the event system for cross-component communication and real-time updates

#### Component Development
- **Hierarchical Structure**: Maintain the hierarchical relationship between Rewards, Levels, and Stamps
- **State Management**: Use the centralized state management patterns for reward state transitions
- **Event Integration**: Leverage the event system for cross-component communication
- **Performance Considerations**: Implement lazy loading and efficient rendering patterns

#### Data Access Patterns
- **Model Methods**: Use instance methods on Reward models for business logic
- **Utility Functions**: Leverage the Rewards and Stamps utility objects for common operations
- **Query Optimization**: Use efficient Realm queries with proper filtering and sorting
- **Caching Strategy**: Implement appropriate caching for frequently accessed data

#### Integration Patterns
- **Widget Integration**: Follow the DataWidget pattern for card integration
- **Service Layer**: Use the service layer for all external API communications
- **Action Layer**: Implement high-level operations through the Actions layer
- **Event Handling**: Use the event system for loose coupling between components

#### Best Practices

**Implementation Guidelines**:
- Use the `Rewards` utility methods for consistent reward state handling across all components
- Implement proper cleanup in widget lifecycle methods to prevent memory leaks
- Follow level navigation patterns consistently for uniform user experience
- Ensure proper transaction recording for stamp operations to maintain audit trail
- Maintain separation between reward display and business logic for maintainability
- Handle all reward states appropriately in custom implementations for robust error handling

### Code Organization

#### File Structure
```
src/
├── lib/
│   ├── rewards.js                 # Core reward utilities and business logic
│   ├── models/Reward/             # Reward model hierarchy
│   │   ├── Reward.js             # Main reward model
│   │   ├── Level.js              # Reward level model
│   │   ├── Stamp.js              # Stamp model
│   │   └── ...                   # Additional reward models
│   ├── widgets/Reward.js          # Reward widget implementation
│   └── common/
│       ├── services/reward.js     # Reward API service
│       └── actions/reward.js      # Reward action layer
├── controllers/Reward.js          # Reward controller
├── components/Reward/             # Reward UI components
│   ├── RewardItem.js             # Individual reward item
│   └── render/                   # Rendering utilities
└── containers/Reward/             # Reward screens
    ├── Rewards.js                # Main reward detail screen
    └── List.js                   # Reward list screen
```

#### Naming Conventions
- **Models**: PascalCase with descriptive names (e.g., `RewardLevel`, `RewardTransaction`)
- **Services**: camelCase with service suffix (e.g., `rewardService`, `stampService`)
- **Components**: PascalCase matching their functionality (e.g., `RewardItem`, `StampProgress`)
- **Utilities**: camelCase with descriptive names (e.g., `stampsStatus`, `levelState`)

### Testing Guidelines

#### Unit Testing
- **Model Testing**: Test all model methods and computed properties
- **Utility Testing**: Comprehensive testing of reward and stamp utilities
- **Service Testing**: Mock external API calls and test service layer
- **Component Testing**: Test component rendering and user interactions

#### Integration Testing
- **Widget Integration**: Test reward widget integration with card system
- **Shopping Integration**: Test stamp issuance through shopping flow
- **Notification Integration**: Test notification triggers and handling
- **Sync Integration**: Test data synchronization and conflict resolution

#### Performance Testing
- **Rendering Performance**: Test large reward lists and complex level structures
- **Memory Usage**: Monitor memory usage during reward navigation
- **Database Performance**: Test query performance with large datasets
- **Network Performance**: Test API performance and error handling

### Security Guidelines

#### Data Protection
- **Sensitive Data**: Ensure reward transaction data is properly secured
- **API Security**: Implement proper authentication for reward API calls
- **Input Validation**: Validate all inputs to prevent injection attacks
- **Audit Logging**: Maintain comprehensive audit logs for reward operations

#### Business Logic Security
- **Fraud Prevention**: Implement multiple validation layers for stamp issuance
- **Rate Limiting**: Implement rate limiting for reward operations
- **Access Control**: Ensure proper access control for reward management
- **Data Integrity**: Maintain data integrity through validation and constraints

## Troubleshooting Guide

### Common Issues

#### Stamp Issuance Problems
**Issue**: Stamps not being issued after purchase
**Diagnosis**:
1. Check reward qualifiers configuration (`qualifiers.issuestamps`)
2. Verify purchase meets minimum spend requirements (`totalPrice.$gte` or `$gt`)
3. Check channel restrictions (`channels` array includes current channel)
4. Verify reward is active and not expired (state validation)
5. Check daily stamp limits (`options.dailyStampLimit`)
6. Validate filter criteria if specified (`qualifiers.issuestamps.filter`)

**Resolution**:
- Review qualifier configuration in reward master data
- Check `spendPerStamp()` calculation against purchase total
- Verify `channels` array includes "perkd" or appropriate channel
- Check reward state using `QUERY.active` conditions
- Validate filter application using sift.js evaluation
- Check sync status for recent updates and deferred actions

#### Level Progression Issues
**Issue**: Levels not progressing after completion
**Diagnosis**:
1. Verify all stamps in level are marked as `stamped: true`
2. Check level completion detection using `Rewards.completedLevel(level)`
3. Verify `atLevel` calculation logic for current level determination
4. Check `completedAt` timestamp setting
5. Verify next level availability and timing constraints
6. Check for data synchronization issues and transaction processing

**Resolution**:
- Review stamp completion status using `stamps.filter(s => s.stamped).length`
- Verify level completion algorithm: `stamps.length === stamped.length`
- Check `atLevel` getter logic for proper level progression
- Ensure `completedAt` timestamp is set correctly
- Verify level ordering and progression constraints
- Check transaction processing and `afterPersist` hooks
- Force data synchronization if needed via `Sync.cache()`

#### UI Performance Issues
**Issue**: Slow rendering of reward lists or details
**Diagnosis**:
1. Check for large datasets without pagination
2. Verify image loading and caching
3. Check for unnecessary re-renders
4. Monitor memory usage during navigation

**Resolution**:
- Implement virtual scrolling for large lists
- Optimize image loading with proper caching
- Use React.memo for expensive components
- Implement proper cleanup in component lifecycle

### Debugging Tools

#### Development Tools
- **Realm Browser**: Inspect local database state and relationships
- **Network Inspector**: Monitor API calls and responses
- **React DevTools**: Debug component state and props
- **Performance Monitor**: Track rendering performance and memory usage

#### Logging and Analytics
- **Event Tracking**: Monitor reward events and user interactions
- **Error Logging**: Comprehensive error logging with context
- **Performance Metrics**: Track key performance indicators
- **User Analytics**: Monitor user engagement and completion rates

### Monitoring and Alerting

#### Key Metrics
- **Stamp Issuance Rate**: Monitor stamp issuance success rate
- **Level Completion Rate**: Track level completion percentages
- **API Response Times**: Monitor reward API performance
- **Error Rates**: Track error rates across reward operations

#### Alert Thresholds
- **High Error Rate**: Alert when error rate exceeds 5%
- **Slow API Response**: Alert when response time exceeds 2 seconds
- **Low Completion Rate**: Alert when completion rate drops below baseline
- **Sync Failures**: Alert on repeated synchronization failures

## Appendices

### A. State Definitions

#### Reward States
- **PENDING**: Reward issued but not yet active (`state == "pending"`)
- **ACTIVE**: Reward is active and accepting stamps (passes `QUERY.active` conditions)
- **COMPLETED**: All levels completed, reward finished (all stamps `stamped: true`)
- **EXPIRED**: Reward past end date, no longer active (`endTime < NOW`)
- **NOT_STARTED**: Reward not yet started (`startTime > NOW`)
- **CANCELLED**: Reward cancelled and hidden (`state == "cancelled"`)

#### Level States
- **ACTIVE**: Level is current and accepting stamps (default state when conditions met)
- **COMPLETED**: All stamps in level completed (`Rewards.completedLevel(level) == true`)
- **EXPIRED**: Level past end date (`endTime < NOW`)
- **NOT_STARTED**: Level not yet available (`startTime > NOW`)
- **FULLREDEEMED**: Level completed and all offers redeemed (`fullyRedeemedAt && offerIds.length === 0`)

#### Stamp States
- **UNSTAMPED**: Stamp not yet earned (`stamped: false`)
- **STAMPED**: Stamp earned and marked (`stamped: true`)
- **NOTIFIED**: User notified of stamp earning (`notifiedAt != null`)
- **EXPIRED**: Stamp opportunity expired (level/reward expired)

#### Transaction States
- **ISSUE**: Stamp issuance transaction (`type: "issue"`)
- **DEDUCT**: Stamp removal transaction (`type: "deduct"`)
- **CARRYOVER**: Stamp transfer transaction (`type: "carryover"`)
- **NOTIFIED**: Transaction notification status (`notifiedAt != null`)

### B. Event Definitions

#### Reward Events
- **reward.updated**: Reward data updated
- **reward.deleted**: Reward removed
- **reward.completed**: Reward fully completed
- **reward.expired**: Reward expired

#### Level Events
- **level.completed**: Level completed
- **level.progressed**: Progress made on level
- **offer.issued**: Offer issued for completed level

#### Stamp Events
- **stamp.issued**: New stamp issued
- **stamp.notified**: User notified of stamp
- **stamps.batch**: Multiple stamps issued

### C. API Reference

#### Reward Service Methods
```javascript
// Fetch rewards by IDs
rewardService.fetch({ ids: ['reward1', 'reward2'] })

// Notify stamp viewing
rewardService.notifyStamps(rewardId, transactionId, view, timestamp)

// Request reward issuance
rewardService.request(masterId, cardId, rewardId, quantity)
```

#### Action Methods
```javascript
// Request reward through action layer
Actions.reward.request({
    masterId: 'master123',
    cardId: 'card456',
    id: 'reward789',
    quantity: 1
})
```

### D. Configuration Examples

#### Reward Qualifier Configuration
```json
{
    "qualifiers": {
        "issuestamps": {
            "totalPrice": { "$gte": 10.00 },
            "categories": ["food", "beverage"],
            "channels": ["perkd", "instore"]
        }
    }
}
```

#### Level Configuration
```json
{
    "level": 1,
    "name": "Bronze Level",
    "description": "Collect 5 stamps to unlock rewards",
    "stamps": [
        { "id": "stamp1", "stamped": false },
        { "id": "stamp2", "stamped": false },
        { "id": "stamp3", "stamped": false },
        { "id": "stamp4", "stamped": false },
        { "id": "stamp5", "stamped": false }
    ]
}
```

For related documentation, see:
- [Application Architecture](app-architecture.md) - Overall application architecture patterns
- [Offer System Documentation](Offer.md) - Integration with promotional offers
- [Card Management Documentation](Card.md) - Card system integration details
