# Bag/Checkout System Technical Reference

## Overview

This document serves as the authoritative technical reference for AI coding agents developing next-generation commerce applications. It provides comprehensive implementation details for the bag/checkout subsystem, focusing on architectural patterns, business logic implementation, and inter-system integration.

**Target Audience**: AI coding agents requiring deep technical understanding to generate sophisticated commerce applications with proper bag/checkout implementation.

**Scope**: Complete technical documentation of the bag/checkout subsystem including architecture, implementation patterns, API specifications, and integration requirements.

## Architecture & Design Rationale

### Core Architectural Decisions

#### Policy-Driven Configuration Architecture

**Why This Pattern**: Enables rapid business rule adaptation without code deployment while maintaining system consistency across diverse merchant implementations.

**Business Drivers**:
- **Market Responsiveness**: Business rules change faster than development cycles
- **Merchant Autonomy**: Business teams need control over rules without technical dependencies  
- **Operational Efficiency**: Emergency rule changes must be deployable immediately
- **A/B Testing**: Policy variations enable experimentation without system risk

**Alternatives Considered**:
- **Hard-coded Business Rules**: Rejected due to inflexibility and deployment overhead
- **Database-driven Rules**: Rejected due to performance and complexity concerns
- **External Rules Engine**: Rejected due to latency and dependency concerns

**Trade-offs Made**:
- **Complexity vs. Adaptability**: Accepted increased system complexity for business agility
- **Performance vs. Flexibility**: Accepted runtime policy resolution overhead for configuration flexibility
- **Development vs. Operations**: Shifted complexity from development to configuration management

#### Two-Phase Transaction Coordination Pattern

**Why This Pattern**: Ensures transaction integrity by reserving offers before payment processing, with automatic rollback on payment failure.

**Business Drivers**:
- **Revenue Protection**: Prevent double-charging, inventory overselling, and offer abuse
- **Customer Experience**: Provide reliable order completion with clear status updates
- **System Reliability**: Handle partial failures gracefully with appropriate rollback mechanisms

**Alternatives Considered**:
- **Single-Phase Processing**: Rejected due to risk of offer double-redemption during payment processing
- **Complex Multi-Phase**: Rejected due to implementation complexity without proportional benefit
- **Optimistic Concurrency**: Rejected due to poor user experience with frequent conflicts

**Trade-offs Made**:
- **Coordination Overhead vs. Reliability**: Accepted offer reservation overhead for transaction consistency
- **Implementation Complexity vs. Integrity**: Balanced complexity with practical transaction reliability
- **Processing Time vs. Accuracy**: Accepted reservation step for accurate offer state management

#### Sophisticated State Management

**Why This Pattern**: Commerce transactions require careful state coordination to prevent revenue loss and ensure customer satisfaction.

**Business Drivers**:
- **Concurrent Access Protection**: Multiple users or processes modifying the same bag simultaneously
- **Cross-Device Continuity**: Customers expect cart contents to persist across sessions and devices
- **Real-time Validation**: Pricing, inventory, and offer changes must be reflected immediately

**Alternatives Considered**:
- **Optimistic Concurrency**: Rejected due to poor user experience with frequent conflicts
- **Session-based State**: Rejected due to cross-device continuity requirements
- **Stateless Architecture**: Rejected due to performance implications of constant recalculation

**Trade-offs Made**:
- **User Experience vs. Data Consistency**: Implemented locking mechanisms that may delay operations
- **Memory vs. Performance**: Maintained state in memory for performance with persistence overhead
- **Simplicity vs. Reliability**: Accepted complex state management for reliable user experience

## Core Classes and Architecture

### Bag Class (`src/lib/checkout/bag.js`)

**Primary Responsibility**: Central state container and calculation engine for shopping cart operations.

```javascript
class Bag {
    #id;                    // Unique bag identifier
    #Currency;              // Currency handling utilities
    #allottedItems;         // Items allocated to Buy X Get Y prerequisites
    #through;               // Source tracking for analytics

    // Core Properties
    name;                   // Bag name for multi-bag scenarios
    items;                  // ItemList - main shopping items
    addons;                 // AddonList - additional services/items
    fulfillment;            // Fulfillment - delivery/pickup configuration
    policy;                 // Policy - business rules and configuration
    
    // Financial State
    totalPrice;             // Final total including all calculations
    subtotalPrice;          // Subtotal before discounts and taxes
    totalDiscounts;         // Total discount amount applied
    totalTax;               // Total tax amount
    currency;               // Currency code
    taxIncluded;            // Boolean - tax calculation mode
    
    // Lifecycle Management
    startTime;              // Bag creation timestamp
    endTime;                // Bag expiration timestamp
    modifiedAt;             // Last modification timestamp
    
    // Discount Management
    discountsApplied;       // DiscountList - applied discounts
    injectedItems;          // InjectedList - items added by promotions
    toQualify;              // Qualification tracking object
    qualified;              // Qualified offers and rewards
}
```

**Key Methods**:

```javascript
// Lifecycle Management
static async use(cardId, name)              // Switch to named bag
persist()                                   // Save bag state
empty()                                     // Clear all items and reset state
get()                                       // Serialize bag state

// Item Management  
addItems(list)                             // Add items with validation
updateItem(variantId, quantity, options)   // Update item quantity/options
removeItems(variantIds)                    // Remove items by variant ID

// Calculation Engine
calculate()                                // Recalculate all totals and discounts
totals(nett)                              // Calculate subtotals and taxes
priceAndTax()                             // Calculate item prices and taxes

// Discount Engine
qualifyDiscounts(targetType)               // Find applicable discounts
bestDiscounts(qualified)                   // Select optimal discount combination
applyDiscounts(discounts)                  // Apply selected discounts
saving(discount)                           // Calculate savings and waste for discount

// Fulfillment Management
setFulfillment(fulfillment)                // Set delivery/pickup options
canFulfill(fulfillment)                    // Validate fulfillment availability

// Policy Management
applyPolicy(policy, currency, taxes, taxIncluded)  // Apply business rules
refreshPolicy()                            // Reload current policies
```

### BagController Class (`src/controllers/Bag.js`)

**Primary Responsibility**: Orchestrates bag operations, manages UI integration, and coordinates with external systems.

```javascript
class BagController {
    #bagId;                 // Bag identifier
    #source;                // Source tracking for analytics
    #from;                  // Origin context tracking

    // Core References
    host;                   // Widget host container
    card;                   // Associated loyalty card
    bag;                    // Bag instance
    policy;                 // Current policy configuration
    cardController;         // Card controller reference
    
    // State Management
    locked;                 // Checkout lock status
    openingBag;             // UI state tracking
    removedDiscounts;       // Manually removed discounts
    
    // UI Integration
    componentId;            // Current UI component ID
    type;                   // Container type for UI rendering
}
```

**Static Methods**:

```javascript
// Policy Management
static policy(policy, cardMaster)          // Merge policies with inheritance
static policyOffers(card)                  // Extract policy-defined offers
static offers(validOffers, policyOffers, excludeOffers, removedDiscounts)  // Combine offer sources
static reward(cardId, limit)               // Get applicable rewards

// Validation
static validateItems(items, master)        // Validate item compatibility
static validateFulfillment(fulfillment, policy)  // Validate fulfillment options
```

**Instance Methods**:

```javascript
// Lifecycle Management
constructor(host, cardController, id, bagId, source)
open(done, through)                        // Open bag UI
close()                                    // Close bag UI and cleanup

// State Management
lockBag()                                  // Lock bag for checkout
unlockBag()                                // Release bag lock
save()                                     // Persist bag state
refreshPolicy()                            // Reload business rules

// Item Operations
addItems(evt)                              // Add items from event
updateItem(variantId, quantity, options)   // Update item details
removeItems(variantIds)                    // Remove items
resetOffers()                              // Clear applied offers

// Checkout Operations
checkout(paymentMethod, fulfillment)       // Initiate checkout process
processCheckout(order, paymentMethod)      // Execute payment processing
postCheckout(result)                       // Handle checkout completion

// Fulfillment Operations
setFulfillment(fulfillment)                // Configure delivery/pickup
setAddons(addon)                           // Add service addons
refreshSpot()                              // Update location context
checkIn(spot)                              // Location-based activation
```

## Fine-Grained Business Logic Documentation

### Bag Lifecycle Management

#### Bag Creation and Initialization

**Creation Pattern**:
```javascript
// Bag instantiation with full state initialization
constructor(id, bag, callbacks) {
    const {
        name, items = [], fulfillment, receipt, totalPrice, currency, taxIncluded,
        taxes, startTime, endTime, policy, injectedItems = [], type, through,
    } = bag;

    this.#id = id;
    this.name = name;
    this.items = new ItemList();
    this.addons = new AddonList();
    this.fulfillment = fulfillment ? new Fulfillment(fulfillment) : undefined;
    this.policy = policy ? new Policy(policy, this) : undefined;
    this.discountsApplied = new DiscountList();
    this.injectedItems = new InjectedList();

    // Financial state initialization
    this.totalPrice = totalPrice || 0;
    this.currency = currency;
    this.taxIncluded = taxIncluded || false;
    this.taxes = taxes || [];

    // Lifecycle timestamps
    this.startTime = startTime;
    this.endTime = endTime;

    // Calculation state
    this.totalDiscounts = 0;
    this.subtotalPrice = 0;
    this.totalTax = 0;
    this.toQualify = {};
    this.qualified = {};

    this.callbacks = callbacks || {};
}
```

**Business Logic**:
- **State Isolation**: Each bag maintains independent state for multi-bag scenarios
- **Lazy Initialization**: Complex objects (Policy, Fulfillment) created only when needed
- **Callback Integration**: Persistence and UI update callbacks registered during creation
- **Financial State Reset**: All calculated values initialized to zero for clean state

#### Bag Persistence Strategy

**Selective Persistence Pattern**:
```javascript
persist() {
    const { through } = this,
        { callbacks, toQualify, policy, discountsApplied, qualified,
          items: bagItems, addons: bagAddons, ...bagData } = this.get(),
        { write } = this.callbacks,
        { offers, reward, fulfillments, ...bagPolicy } = policy || {},

        // Strip transient state from discounts
        discounts = discountsApplied.map(({ qualifiers, immutable, image, ...discount }) => discount),

        // Strip status from addons
        addons = bagAddons.map(({ status, ...item }) => item),

        // Combine items and addons, strip status
        items = [...bagItems.map(({ status, ...item }) => item), ...addons];

    if (write) {
        return write({
            ...bagData,
            items,
            through,
            policy: bagPolicy,
            discountsApplied: discounts,
            modifiedAt: newDate(),
        });
    }
    return Promise.resolve();
}
```

**Business Logic**:
- **Transient State Exclusion**: Callbacks, qualification state, and UI state not persisted
- **Status Stripping**: Runtime status information removed from items and addons
- **Policy Filtering**: Only persistent policy configuration saved, excluding runtime offers
- **Timestamp Management**: Automatic modification timestamp for change tracking

#### Bag Expiration and TTL Management

**TTL-Based Expiration**:
```javascript
// Automatic expiration setting during item addition
if (this.items.length === 0 && !this.policy.endTime) {
    const { ttl } = _.Preference.appSettings('bag') || {},
        { itemsTTL = ttl } = this.policy.options;

    this.endTime = new Date(newDate().getTime() + (itemsTTL * DAY));
}
```

**Business Logic**:
- **Conditional Expiration**: TTL only set for empty bags without explicit end time
- **Policy Override**: Bag-specific TTL can override global application settings
- **Resource Management**: Prevents abandoned carts from consuming system resources indefinitely
- **Merchant Flexibility**: Different merchants can set appropriate cart retention policies

### Concurrent Access Control and State Synchronization

#### Checkout Locking Mechanism

**Lock Implementation**:
```javascript
lockBag() {
    this.locked = true;
    this.addListeners();
}

unlockBag() {
    this.locked = false;
    this.removeListeners();
}

// Policy refresh respects lock state
refreshPolicy() {
    if (this.locked) return;  // Skip refresh during checkout

    const { card, policy, removedDiscounts = [] } = this,
        { excludeOffers, limit } = policy,
        policyOffers = BagController.policyOffers(card),
        offers = BagController.offers(_.Offer.valids(card.id, true, card.master?.merchantId),
                                     policyOffers, excludeOffers, removedDiscounts),
        reward = BagController.reward(card.id, limit);

    this.applyPolicy({ ...policy, offers, reward });
}
```

**Business Logic**:
- **Critical Section Protection**: Prevents concurrent modifications during checkout
- **Event Listener Management**: Automatic listener registration/deregistration with lock state
- **Policy Refresh Blocking**: Prevents business rule changes during transaction processing
- **Guaranteed Unlock**: Finally blocks ensure unlock regardless of success/failure

#### Cross-Device State Synchronization

**Multi-Device Bag Access**:
```javascript
static async use(cardId, name) {
    const widgetData = await Bag.widgetData(cardId).catch(() => ({})),
        { data = {} } = widgetData,
        { bags = [] } = data,
        bag = bags.find(b => b.name === name),
        newBag = { name };

    if (!bag) {
        data.bags = [...bags, newBag];
    } else if (!Bag.isValid(bag)) {
        // Handle invalid bag state
        Object.assign(widgetData, { badge: { unread: null, valid: null } });
    }

    data.current = name;
    return Bag.widgetData(cardId, data);
}
```

**Business Logic**:
- **Bag Discovery**: Automatic bag creation if named bag doesn't exist
- **Validity Checking**: Invalid bags reset with badge state clearing
- **Current Bag Tracking**: Active bag name maintained for session continuity
- **Cross-Device Sync**: Widget data synchronization enables multi-device access

### Sophisticated Discount Engine Logic

#### Discount Stacking Rules Implementation

**Stacking Rule Constants**:
```javascript
// Discount usage patterns
const ALWAYS = 'always';    // Can always use, e.g., membership discount
const ALONE = 'alone';      // Order-level: cannot be used with other discounts, except 'always'
const ONE = 'one';          // Order-level: cannot be used with other discounts, except 'always'
const COMBINE = 'combine';  // Item-level: can be used with 'combine' or 'always' discounts
const SINGLE = 'single';    // Item-level: cannot be combined with any other discount on same item
```

**Stacking Logic Implementation**:
```javascript
bestDiscounts(qualified) {
    if (!qualified.length) return [];

    const mostSavingFirst = qualified
        .sort((a, b) => a.waste - b.waste)      // Lowest wastage when equal saving
        .sort((a, b) => b.save - a.save)        // Highest savings first
        .sort((a, b) => b.priority - a.priority), // Highest priority first

        { combine, alones } = mostSavingFirst.reduce((res, offer) => {
            const { discount } = offer,
                { use } = discount;

            if (use === ALONE) res.alones.push(offer);
            else if (use === ONE) {
                if (!res.one) {
                    res.one = offer;
                    res.combine.push(offer);    // Use first & best among 'one'
                }
            } else res.combine.push(offer);
            return res;
        }, { combine: [], alones: [], one: null }),

        combinedSaving = combine.reduce((total, i) => total + i.save, 0),
        [alone] = alones;

    // Return alone discount if it saves more than combined discounts
    return (alone && alone.save >= combinedSaving) ? [alone] : combine;
}
```

**Business Logic**:
- **Multi-Criteria Optimization**: Sophisticated sorting algorithm using priority, savings, and waste minimization
- **Dynamic Re-optimization**: Offer savings recalculated after each discount application for optimal selection
- **Intelligent Combination Logic**: Compares alone vs. combined savings to maximize customer benefit
- **Waste Minimization**: System selects combinations that minimize unused discount value
- **Mutual Exclusivity**: ALONE discounts prevent combination with any other offers

**Algorithm Sophistication**: This is not basic filtering - the system implements sophisticated optimization algorithms including multi-criteria sorting, dynamic re-evaluation, and intelligent combination selection to maximize customer savings while respecting business rules.

#### Allocation Methods Implementation

**ACROSS vs EACH Allocation**:
```javascript
allocateDiscount(discount, targets) {
    const { allocationMethod, isBuyXgetY, entitled } = discount,
        { entitledQuantity } = entitled;

    switch (allocationMethod) {
        case EACH: {    // Value applied onto each item
            const { entitledTimes, prerequisited } = this.entitlement(discount),
                unlimited = (entitledTimes === null);

            if (!unlimited && entitledTimes === 0) return 0;

            this.reservePrerequisite(prerequisited);

            const totalTargetQty = targets.reduce((total, item) =>
                    total + item.quantity - (isBuyXgetY ? this.allottedQty(item) : 0), 0),
                timesY = Math.floor(totalTargetQty / entitledQuantity),
                entitledQty = isBuyXgetY
                    ? (unlimited ? timesY : entitledTimes) * entitledQuantity
                    : entitledTimes;

            const { amount } = this.discountAmountEach(discount, targets, entitledQty, unlimited, index);
            return amount;
        }

        case ACROSS: {  // Value spread across all entitled items
            const totalPrice = targets.reduce((total, item) => total + item.price, 0),
                discountAmount = discount.discountAmount(totalPrice);

            // Proportional allocation across items
            targets.forEach(item => {
                const proportion = item.price / totalPrice,
                    itemDiscount = discountAmount * proportion;
                item.discount.allocations.push({ index, amount: itemDiscount });
            });

            return discountAmount;
        }
    }
}
```

**Business Logic**:
- **EACH Method**: Discount value applied to each qualifying item individually
- **ACROSS Method**: Discount value spread proportionally across all entitled items
- **Quantity Tracking**: Precise tracking of allocated quantities for Buy X Get Y
- **Proportional Distribution**: ACROSS allocation maintains fairness across mixed carts

#### Buy X Get Y Sophisticated Patterns

**Prerequisite and Entitlement Calculation**:
```javascript
entitlement(discount) {
    const { isBuyXgetY, entitled, allocation } = discount,
        { remain } = allocation,
        { prerequisiteQuantity, entitledQuantity, items = [] } = entitled;

    if (!isBuyXgetY) return { entitledTimes: remain, prerequisited: [] };

    // Buy X get Y logic
    const ratioX = prerequisiteQuantity / (prerequisiteQuantity + entitledQuantity),
        itemsX = this.prerequisiteTargets(discount),
        entitledItems = this.entitledTargets(discount),

        // Calculate available X quantity and candidates
        { quantityX, candidatesX } = itemsX.reduce((res, { variantId, quantity }) => {
            const allocateQty = quantity - this.allottedQty({ variantId });

            if (allocateQty > 0) {
                res.candidatesX.push({ variantId, quantity: allocateQty });
            }
            return {
                quantityX: res.quantityX + allocateQty,
                candidatesX: res.candidatesX,
            };
        }, { quantityX: 0, candidatesX: [] }),

        // Calculate how many times the offer can be applied
        timesX = Math.floor(quantityX / prerequisiteQuantity),
        entitledTimes = remain === null ? timesX : Math.min(remain, timesX);

    return { entitledTimes, prerequisited: candidatesX };
}
```

**Common Item Handling**:
```javascript
// When items qualify as both prerequisite and entitlement
prerequisiteTargets(discount) {
    const { qualifiers } = discount,
        { prerequisite } = qualifiers,
        conditions = sift(prerequisite);

    return this.items.filter(conditions)
        .filter(i => i.price > 0 && !i.status.soldout);
}

entitledTargets(discount) {
    const { qualifiers, use, offerMasterId, isBuyXgetY } = discount,
        { entitled } = qualifiers,
        conditions = sift(entitled);

    const existing = this.items.filter(conditions)
        .filter(i => i.price > 0 && !i.status.soldout)
        .filter(item => {    // Handle 'single' use exclusions
            const { allocations } = item.discount;
            if (allocations.length === 0) return true;
            if (use === SINGLE) return false;

            const [{ index }] = allocations;
            return this.discountsApplied[index].use !== SINGLE;
        });

    return isBuyXgetY
        ? existing.sort((a, b) => a.unitPrice - b.unitPrice)    // Lowest price first for Y
        : existing.sort((a, b) => b.unitPrice - b.unitPrice);   // Highest price first for regular
}
```

**Business Logic**:
- **Prerequisite Tracking**: Complex logic for qualifying items (X) with quantity aggregation
- **Entitlement Calculation**: Automatic computation of entitled quantities (Y) based on prerequisite fulfillment
- **Common Item Logic**: Sophisticated handling when items qualify as both prerequisite and entitlement
- **Price Optimization**: Different sorting strategies for prerequisites vs. entitlements

### Validation Rules and Error Handling Mechanisms

#### Item Validation Pipeline

**Multi-Layer Validation**:
```javascript
addItems(list) {
    const errors = [];

    // Validate each item in the list
    list.forEach(item => {
        const itemErrors = this.validateItem(item);
        if (itemErrors.length > 0) {
            errors.push(...itemErrors);
        }
    });

    if (errors.length === 0) {
        // Set TTL for empty bags
        if (this.items.length === 0 && !this.policy.endTime) {
            const { ttl } = _.Preference.appSettings('bag') || {},
                { itemsTTL = ttl } = this.policy.options;

            this.endTime = new Date(newDate().getTime() + (itemsTTL * DAY));
        }

        this.items.add(list, this);
        this.calculate();
        $.Event.emit(EVENT.itemsAdded, { items: list });
    } else {
        $.watch('[Bag]addItems', 'invalid_item', errors);
    }

    this.emitChange(errors);
}

validateItem(item) {
    const errors = [],
        { variantId, productId, quantity, unitPrice } = item;

    // Required field validation
    if (!variantId) errors.push({ field: 'variantId', message: 'Variant ID required' });
    if (!productId) errors.push({ field: 'productId', message: 'Product ID required' });
    if (!quantity || quantity <= 0) errors.push({ field: 'quantity', message: 'Valid quantity required' });
    if (!unitPrice || unitPrice < 0) errors.push({ field: 'unitPrice', message: 'Valid unit price required' });

    // Business rule validation
    if (this.policy && !this.policy.modify.items) {
        errors.push({ field: 'policy', message: 'Item modification not allowed' });
    }

    return errors;
}
```

**Business Logic**:
- **Input Sanitization**: Required field validation prevents invalid data entry
- **Business Rule Enforcement**: Policy-based validation ensures compliance with merchant rules
- **Batch Validation**: All items validated before any are added to prevent partial state
- **Error Aggregation**: All validation errors collected and reported together

#### Discount Validation and Conflict Resolution

**Discount Validation Pipeline**:
```javascript
validate() {
    const { offerId, kind, value, targetType, targetSelection, allocation } = this,
        errors = [];

    // Basic field validation
    if (!offerId) errors.push({ message: `Discount.offerId required` });
    if (!KIND.includes(kind)) {
        errors.push({ message: `Discount.kind invalid: ${kind} (offerId: ${offerId})` });
    }
    if (typeof value !== 'number' || value < 0) {
        errors.push({ message: `Discount.value invalid: ${value} (offerId: ${offerId})` });
    }

    // Target validation
    if (!TARGET_TYPE.includes(targetType)) {
        errors.push({ message: `Discount.targetType invalid: ${targetType} (offerId: ${offerId})` });
    }
    if (!TARGET_SELECTION.includes(targetSelection)) {
        errors.push({ message: `Discount.targetSelection invalid: ${targetSelection} (offerId: ${offerId})` });
    }

    // Allocation validation
    if (!allocation || !ALLOCATION_METHOD.includes(allocation.method)) {
        errors.push({ message: `Discount.allocation.method invalid: ${allocation?.method} (offerId: ${offerId})` });
    }

    return errors;
}
```

**Exclusion and Conflict Detection**:
```javascript
// Check for excluded offers
entitledTargets(discount) {
    const { use, offerMasterId, discountsApplied } = discount;

    return this.items.filter(item => {
        // Filter out items with conflicting discounts
        const { allocations } = item.discount;

        // Check for 'single' use conflicts
        if (allocations.length > 0 && use === SINGLE) return false;

        // Check for excluded offers
        const excludedIndex = discountsApplied.indexOfExcludedOffer(offerMasterId);
        return !item.discount.allocations.find(a => a.index === excludedIndex);
    });
}
```

**Business Logic**:
- **Schema Validation**: Comprehensive validation of discount structure and values
- **Conflict Detection**: Automatic detection of mutually exclusive offers
- **Exclusion Management**: Enforcement of offer exclusion rules at item level
- **Error Context**: Detailed error messages with offer identification for debugging

## Inter-System Integration Patterns

### Offers System Integration

#### Offer Reservation and Transaction Management

**Two-Phase Transaction Coordination**:
```javascript
async checkout(paymentMethod, fulfillment) {
    const self = this;

    try {
        // Phase 1: Reserve offers and create order
        const order = await self.createOrder(fulfillment);
        const offerIds = self.bag.discountsApplied.map(d => d.offerId).filter(Boolean);

        if (offerIds.length > 0) {
            await _.Offer.reserve(offerIds, self.card.master.id, order.id);
        }

        // Phase 2: Process payment with automatic rollback on failure
        const paymentResult = await self.processCheckout(order, paymentMethod);

        if (!paymentResult.success) {
            await self.rollbackTransaction(order, offerIds);
        }

        return paymentResult;

    } catch (error) {
        // Automatic rollback on any failure
        await self.rollbackTransaction(order, offerIds);
        throw error;
    }
}

async rollbackTransaction(order, offerIds) {
    if (offerIds.length > 0) {
        await _.Offer.recover(offerIds);  // Release reserved offers
    }
    // Additional cleanup logic
}
```

**Offer Qualification Engine Integration**:
```javascript
qualifyDiscounts(targetType) {
    const { policy } = this,
        { offers = [] } = policy,
        NOW = newDate(),
        context = App.ContextBuilder(this, NOW);  // Build qualification context

    return offers
        .filter(({ discount }) => discount.targetType === targetType)
        .map(offer => {
            const { discount } = offer,
                qualified = this.qualifyOffer(discount, context);

            if (qualified) {
                const { save, waste } = this.saving(discount);
                return { ...offer, save, waste, qualified: true };
            }
            return null;
        })
        .filter(Boolean)
        .sort((a, b) => b.save - a.save);  // Sort by savings
}

qualifyOffer(discount, context) {
    const { qualifiers } = discount,
        { redeem, prerequisite, entitled } = qualifiers;

    // Order-level qualification (redeem)
    if (redeem && !sift(redeem)(context)) return false;

    // Item-level qualification (prerequisite & entitled)
    const prerequisiteItems = prerequisite ? this.items.filter(sift(prerequisite)) : [];
    const entitledItems = entitled ? this.items.filter(sift(entitled)) : this.items;

    return prerequisiteItems.length > 0 && entitledItems.length > 0;
}
```

**Business Logic**:
- **Offer Reservation**: Offers reserved before payment to prevent double-redemption
- **Qualification Logic**: Sift-based query evaluation for offer qualification rules
- **Automatic Rollback**: Failed transactions automatically restore offer availability through `_.Offer.recover()`
- **Dynamic Re-evaluation**: Offer eligibility recalculated after each discount application

### Payment System Integration

The bag/checkout system integrates with the payment system for method selection, validation, and transaction coordination.

**Key Integration Points**:
- **Context-Aware Method Selection**: Payment options filtered based on transaction context and policies
- **Async Payment Handling**: Different cleanup logic for direct, sync, and async payment methods
- **Transaction Coordination**: Payment success/failure coordination with offer reservations
- **Metadata Integration**: Payment requests include bag and card context for reconciliation

*For detailed payment system integration patterns, method selection algorithms, and transaction processing mechanics, see [Payment System Documentation](./payments.md#bag-and-checkout-integration)*

### Inventory Management Integration

#### Allocation Tracking and Soldout Handling

**Soldout Status Integration**:
```javascript
calculate() {
    // Filter out soldout items from calculations
    const activeItems = this.items.filter(item => !item.status.soldout);

    // Calculate subtotals excluding soldout items
    this.subtotalPrice = activeItems.reduce((total, item) => total + item.price, 0);

    // Apply discounts only to active items
    const itemDiscounts = this.qualifyDiscounts(ITEM, activeItems);
    const bestItemDiscounts = this.bestDiscounts(itemDiscounts);

    // Soldout items remain visible but excluded from totals
    this.items.forEach(item => {
        if (item.status.soldout) {
            item.discount.allocations = [];  // Clear any discount allocations
        }
    });
}
```

**Allocation Tracking for Buy X Get Y**:
```javascript
allotQuantity(item, quantity) {
    const { variantId } = item;

    if (!this.#allottedItems[variantId]) {
        this.#allottedItems[variantId] = 0;
    }

    this.#allottedItems[variantId] += quantity;
}

allottedQty(item) {
    const { variantId } = item;
    return this.#allottedItems[variantId] || 0;
}

reservePrerequisite(prerequisited) {
    // Temporarily reserve prerequisite items during calculation
    prerequisited.forEach(({ variantId, quantity }) => {
        this.#reserved = this.#reserved || {};
        this.#reserved[variantId] = (this.#reserved[variantId] || 0) + quantity;
    });
}

releaseReserved() {
    // Release temporary reservations
    this.#reserved = {};
}
```

**Business Logic**:
- **Soldout Exclusion**: Items marked soldout excluded from calculations but remain visible
- **Allocation Tracking**: Precise tracking of allocated quantities per variant for discount prerequisites
- **Temporary Reservation**: Prerequisite items temporarily reserved during Buy X Get Y calculation
- **Quantity Validation**: Available quantity calculated as total minus already allocated amounts

### Cards System Binding

#### Policy Inheritance and Member-Specific Behavior

**Policy Resolution Hierarchy**:
```javascript
static policy(policy, cardMaster) {
    const { fulfillments, bagPolicy } = cardMaster,
        // Deep merge with precedence: runtime policy > cardMaster.bagPolicy > cardMaster.fulfillments
        res = merge({}, bagPolicy || {}, policy || {});

    // Automatic fulfillment integration when not explicitly overridden
    if (!Object.values(policy?.fulfillments || {}).some(f => f)) {
        Object.assign(res, { fulfillments });
    }

    return res;
}
```

**Card-Specific Offer Integration**:
```javascript
static policyOffers(card) {
    const NOW = newDate(),
        { master, endTime } = card;

    // Check card expiration
    if (endTime && (new Date(endTime) < NOW)) return [];

    const { bagPolicy } = master,
        cardImage = master.image(),
        { offers = [] } = bagPolicy || {};

    // Transform policy offers with card context
    return offers.map(o => ({
        ...o,
        id: o.discount.offerMasterId,
        masterId: o.discount.offerMasterId,
        images: o.images?.length ? o.images : [{ url: cardImage }],  // Fallback to card image
    }));
}
```

**Member-Only Shopping Logic**:
```javascript
refreshPolicy() {
    if (this.locked) return;  // Skip during checkout

    const { card, policy, removedDiscounts = [] } = this,
        { excludeOffers, limit } = policy,

        // Get card-specific policy offers
        policyOffers = BagController.policyOffers(card),

        // Combine with live offers, filtered by card membership
        offers = BagController.offers(
            _.Offer.valids(card.id, true, card.master?.merchantId),
            policyOffers,
            excludeOffers,
            removedDiscounts
        ),

        // Get card-specific rewards
        reward = BagController.reward(card.id, limit);

    this.applyPolicy({ ...policy, offers, reward });
}
```

**Business Logic**:
- **Hierarchical Policy Resolution**: Runtime policies override CardMaster policies override defaults
- **Card Lifecycle Integration**: Expired cards return empty offer lists
- **Image Fallback Strategy**: Policy offers inherit card master images when not specified
- **Membership Filtering**: Offers filtered by card membership and merchant association

### Places System Integration

#### Location-Aware Fulfillment and Geofencing

**Location-Based Fulfillment Validation**:
```javascript
async refreshSpot() {
    const { card } = this.host,
        { fulfillments } = this.bag.policy,
        opts = { fulfillments: Object.keys(fulfillments) },
        spot = await Spot.get(card.placeListIds, opts);

    await this.checkIn(spot);
}

async checkIn(spot) {
    if (!spot) return;

    const { bag } = this,
        { fulfillment } = bag,
        { type, destination } = fulfillment || {};

    // Validate current fulfillment against new location
    if (fulfillment && !this.canFulfillAtLocation(fulfillment, spot)) {
        // Reset fulfillment if no longer valid at current location
        await bag.setFulfillment(null);
    }

    // Update location context
    this.spot = spot;
    this.refreshPolicy();  // Refresh with new location context
}

canFulfillAtLocation(fulfillment, spot) {
    const { type, destination } = fulfillment,
        { placeId, placeCategory } = spot;

    switch (type) {
        case 'store':
        case 'dinein':
            // Must be at the specific location
            return destination?.placeId === placeId;

        case 'pickup':
            // Must be at a pickup-enabled location
            return spot.capabilities?.includes('pickup');

        case 'delivery':
            // Check if location is within delivery zone
            return this.isInDeliveryZone(spot, destination);

        default:
            return true;
    }
}
```

**Geofenced Commerce Activation**:
```javascript
// Check-in requirements for specific fulfillment types
const CHECK_IN = ['store', 'dinein', 'vending'];

async validateFulfillmentAvailability(fulfillment) {
    const { type, destination } = fulfillment,
        { policy } = this.bag,
        { fulfillments } = policy;

    if (CHECK_IN.includes(type)) {
        // Require physical presence for in-store fulfillment
        if (!this.spot || this.spot.placeId !== destination?.placeId) {
            throw new Error('Physical presence required for this fulfillment type');
        }
    }

    // Use policy engine for detailed availability checking
    return policy.fulfillments.availability(
        this.card.master.id,
        fulfillment,
        this.bag.items.checkout(),
        this.spot?.placeCategory || 'store'
    );
}
```

**Business Logic**:
- **Proximity-Based Activation**: Commerce features activated based on customer location
- **Fulfillment Validation**: Location changes trigger fulfillment option re-validation
- **Geofenced Access**: Certain fulfillment types require physical presence at specific locations
- **Dynamic Policy Updates**: Location changes trigger policy refresh with new context

## Implementation-Specific Details

### Event-Driven Coordination Patterns

#### Event System Integration

**Event Constants and Coordination**:
```javascript
const EVENT = {
    Bag: BagEvt,           // Bag-specific events
    WidgetData: WidgetDataEvt,  // Widget data synchronization
    Reward,                // Reward system events
    Offer,                 // Offer system events
    Location: LocationEvt, // Location-based events
    Sync: SyncEvt         // Cross-system synchronization
};

// Key bag events
const BagEvt = {
    addItems: 'Bag.addItems',
    updateItems: 'Bag.updateItems',
    removeItems: 'Bag.removeItems',
    initiateCheckout: 'Bag.initiateCheckout',
    checkoutComplete: 'Bag.checkoutComplete',
    policyRefresh: 'Bag.policyRefresh'
};
```

**Event-Driven State Synchronization**:
```javascript
addListeners() {
    // Listen for external state changes during checkout
    $.Event.on(EVENT.Offer.updated, this.handleOfferUpdate, this);
    $.Event.on(EVENT.Location.changed, this.handleLocationChange, this);
    $.Event.on(EVENT.Reward.earned, this.handleRewardUpdate, this);
}

removeListeners() {
    $.Event.off(EVENT.Offer.updated, this.handleOfferUpdate, this);
    $.Event.off(EVENT.Location.changed, this.handleLocationChange, this);
    $.Event.off(EVENT.Reward.earned, this.handleRewardUpdate, this);
}

handleOfferUpdate(evt) {
    if (this.locked) return;  // Ignore during checkout

    const { offerId, status } = evt;

    // Remove offer if it becomes unavailable
    if (status === 'expired' || status === 'soldout') {
        this.removeDiscount(offerId);
    }

    // Refresh policy to pick up new offers
    this.refreshPolicy();
}

handleLocationChange(evt) {
    const { spot } = evt;
    this.checkIn(spot);  // Re-validate fulfillment options
}
```

**Message Flow Patterns**:
```javascript
bagActions(evt, data) {
    const { card, id, bag, bagController, bagType } = this;

    if (!bag) {
        $.watch('[BodyController]bagActions', 'no_bag', evt);
        return Promise.reject(new Error('no_bag'));
    }

    // Set source context if not already set
    if (bagController && (!bagController.source || !bag?.items?.length)) {
        bagController.source = data.source || { name: 'message', id };
    }

    const ctxId = bagType === MESSAGE ? id : card.id,
        first = !bag.items.length,
        items = data.items.map(i => ({
            ...i,
            variantId: String(i.variantId),
            productId: String(i.productId)
        })),
        bagData = { ...bag.get(), id: bagController.bagId };

    // Emit event with full context
    $.Event.emit(evt, {
        source: bagController.source,
        ...data,
        cardId: card.id,
        messageId: id,
        bag: bagData,
        ctxId,
        items,
        first
    });

    return Promise.resolve({ id: bagData.id });
}
```

**Business Logic**:
- **Listener Management**: Automatic event listener registration/deregistration with bag lifecycle
- **Lock-Aware Processing**: Events ignored during checkout to prevent state corruption
- **Context Propagation**: Events include full context for cross-system coordination
- **Source Tracking**: Event sources tracked for analytics and debugging

### Performance Optimization Strategies

#### Lazy Calculation and Caching

**Deferred Computation Pattern**:
```javascript
calculate() {
    // Only recalculate if items have changed
    if (!this.needsRecalculation) return;

    const original = {
        subtotalPrice: this.subtotalPrice,
        totalTax: this.totalTax,
        totalDiscounts: this.totalDiscounts
    };

    // Calculate base totals first
    this.totals(original);

    // Qualify and apply discounts (expensive operation)
    const itemDiscounts = this.qualifyDiscounts(ITEM);
    const bestItemDiscounts = this.bestDiscounts(itemDiscounts);

    // Apply discounts and recalculate
    this.restoreInjectedItems();
    const itemsDiscounts = this.applyDiscounts(bestItemDiscounts);

    // Mark as calculated
    this.needsRecalculation = false;
    this.lastCalculated = newDate();
}

// Trigger recalculation only when necessary
addItems(list) {
    this.items.add(list, this);
    this.needsRecalculation = true;  // Mark for recalculation
    this.calculate();  // Calculate immediately for UI responsiveness
}

updateItem(variantId, quantity, options) {
    const updated = this.items.update(variantId, quantity, options);
    if (updated) {
        this.needsRecalculation = true;
        this.calculate();
    }
}
```

**Intelligent Caching Strategy**:
```javascript
// Cache expensive qualification results
qualifyDiscounts(targetType) {
    const cacheKey = `${targetType}_${this.lastCalculated}_${this.items.length}`;

    if (this.qualificationCache[cacheKey]) {
        return this.qualificationCache[cacheKey];
    }

    const qualified = this.performQualification(targetType);
    this.qualificationCache[cacheKey] = qualified;

    return qualified;
}

// Clear cache when items change
emitChange(errors) {
    this.qualificationCache = {};  // Clear qualification cache
    this.needsRecalculation = true;

    if (this.callbacks.write) {
        this.persist();  // Persist changes
    }

    // Emit change event for UI updates
    $.Event.emit(EVENT.Bag.changed, {
        bag: this.get(),
        errors: errors || []
    });
}
```

**Business Logic**:
- **Lazy Calculation**: Expensive calculations deferred until required
- **Change Detection**: Recalculation triggered only when items or policies change
- **Cache Invalidation**: Qualification cache cleared when bag state changes
- **UI Responsiveness**: Immediate calculation for user-facing operations

## Edge Cases and Failure Scenarios

### Comprehensive Error Conditions and Recovery

#### Concurrent Modification Handling

**Race Condition Prevention**:
```javascript
async checkout(paymentMethod, fulfillment) {
    // Acquire checkout lock
    this.lockBag();

    try {
        // Validate bag state hasn't changed during UI interaction
        const currentHash = this.bag.stateHash();
        if (this.lastKnownHash && this.lastKnownHash !== currentHash) {
            throw new Error('Bag state changed during checkout preparation');
        }

        // Create order with current state snapshot
        const order = await this.createOrder(fulfillment);

        // Reserve offers atomically
        const offerIds = this.bag.discountsApplied.map(d => d.offerId).filter(Boolean);
        if (offerIds.length > 0) {
            await _.Offer.reserve(offerIds, this.card.master.id, order.id);
        }

        // Process payment
        const result = await this.processCheckout(order, paymentMethod);

        return result;

    } catch (error) {
        // Automatic rollback on any failure
        await this.rollbackCheckout(order, offerIds);
        throw error;

    } finally {
        // Guaranteed unlock
        this.unlockBag();
    }
}

stateHash() {
    // Generate hash of critical bag state for change detection
    const criticalState = {
        items: this.items.map(i => ({ variantId: i.variantId, quantity: i.quantity })),
        discounts: this.discountsApplied.map(d => d.offerId),
        fulfillment: this.fulfillment?.type,
        totalPrice: this.totalPrice
    };

    return hashObject(criticalState);
}
```

#### Network Resilience and Offline Capability

**Offline Operation Patterns**:
```javascript
async persist() {
    try {
        // Attempt online persistence first
        await this.persistOnline();

    } catch (error) {
        if (this.isNetworkError(error)) {
            // Queue for offline persistence
            await this.persistOffline();
            this.queueForSync();
        } else {
            throw error;  // Re-throw non-network errors
        }
    }
}

async persistOffline() {
    const bagData = this.get();

    // Store in local storage with timestamp
    await LocalStorage.set(`bag_${this.id}`, {
        ...bagData,
        offline: true,
        lastModified: newDate(),
        syncPending: true
    });
}

queueForSync() {
    // Add to sync queue for when network returns
    SyncQueue.add({
        type: 'bag_update',
        bagId: this.id,
        priority: 'high',
        retryCount: 0,
        maxRetries: 3
    });
}
```

#### Data Consistency Guarantees

**Rollback Procedures**:
```javascript
async rollbackCheckout(order, offerIds) {
    const rollbackTasks = [];

    // Rollback offer reservations
    if (offerIds.length > 0) {
        rollbackTasks.push(
            _.Offer.recover(offerIds).catch(err =>
                $.watch('[Bag]rollback', 'offer_recovery_failed', { offerIds, error: err })
            )
        );
    }

    // Rollback inventory allocations
    if (order?.inventoryAllocations) {
        rollbackTasks.push(
            Inventory.release(order.inventoryAllocations).catch(err =>
                $.watch('[Bag]rollback', 'inventory_release_failed', { order: order.id, error: err })
            )
        );
    }

    // Rollback payment authorization (if any)
    if (order?.paymentAuthId) {
        rollbackTasks.push(
            Payments.void(order.paymentAuthId).catch(err =>
                $.watch('[Bag]rollback', 'payment_void_failed', { authId: order.paymentAuthId, error: err })
            )
        );
    }

    // Execute all rollback tasks in parallel
    await Promise.allSettled(rollbackTasks);

    // Log rollback completion
    $.log(`Checkout rollback completed for order ${order?.id}`, 'rollback', 'bag');
}
```

**Business Logic**:
- **State Hash Validation**: Prevents checkout with stale bag state
- **Atomic Operations**: All-or-nothing approach to critical operations
- **Graceful Degradation**: Offline capability with sync queue for network recovery
- **Comprehensive Rollback**: All allocated resources released on failure

## API Patterns and Method Signatures

### Core API Specifications

#### Bag Class Public API

```javascript
// Static Methods
static async use(cardId, name): Promise<Bag>
static isValid(bag): boolean
static async widgetData(cardId, data?): Promise<Object>

// Instance Methods - Lifecycle
constructor(id, bag, callbacks)
get(): Object
persist(): Promise<void>
empty(): void

// Instance Methods - Item Management
addItems(list: Item[]): void
updateItem(variantId: string, quantity: number, options?: Object): boolean
removeItems(variantIds: string[]): void

// Instance Methods - Calculation
calculate(): void
totals(original: Object): void
priceAndTax(): Object
round(amount: number): number

// Instance Methods - Discounts
qualifyDiscounts(targetType: string): Object[]
bestDiscounts(qualified: Object[]): Object[]
applyDiscounts(discounts: Object[]): number
saving(discount: Discount): { save: number, waste: number }

// Instance Methods - Fulfillment
setFulfillment(fulfillment: Object): Promise<Object>
canFulfill(fulfillment: Object): boolean

// Instance Methods - Policy
applyPolicy(policy: Object, currency?: string, taxes?: Object[], taxIncluded?: boolean): Object[]
```

#### BagController Class Public API

```javascript
// Static Methods
static policy(policy: Object, cardMaster: Object): Object
static policyOffers(card: Object): Object[]
static offers(validOffers: Object[], policyOffers: Object[], excludeOffers?: string[], removedDiscounts?: string[]): Object[]
static reward(cardId: string, limit?: Object): Object
static validateItems(items: Object[], master: Object): Object[]
static validateFulfillment(fulfillment: Object, policy: Object): Object[]

// Instance Methods - Lifecycle
constructor(host, cardController, id, bagId, source)
open(done?: Function, through?: string): Promise<string>
close(): void

// Instance Methods - State Management
lockBag(): void
unlockBag(): void
save(): Promise<void>
refreshPolicy(): void

// Instance Methods - Operations
addItems(evt: Object): Promise<Object>
updateItem(variantId: string, quantity: number, options?: Object): Promise<boolean>
removeItems(variantIds: string[]): Promise<void>
resetOffers(): void

// Instance Methods - Checkout
checkout(paymentMethod: Object, fulfillment?: Object): Promise<Object>
processCheckout(order: Object, paymentMethod: Object): Promise<Object>
postCheckout(result: Object): Promise<void>

// Instance Methods - Fulfillment
setFulfillment(fulfillment: Object): Promise<Object>
setAddons(addon: Object): Promise<Object>
refreshSpot(): Promise<void>
checkIn(spot: Object): Promise<void>
```

This comprehensive technical reference provides AI coding agents with the detailed implementation knowledge needed to generate sophisticated, production-ready bag/checkout systems that properly integrate with the broader commerce ecosystem.

