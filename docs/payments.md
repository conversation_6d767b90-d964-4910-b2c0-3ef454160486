# Perkd Payments Management System - Technical Reference

## Table of Contents

- [Overview](#overview)
- [System Architecture](#system-architecture)
  - [Core Components](#core-components)
  - [Payment Providers](#payment-providers)
  - [Payment Methods](#payment-methods)
- [Payment Method Categorization and Business Logic](#payment-method-categorization-and-business-logic)
  - [Method Classification System](#method-classification-system)
  - [Device Capability Detection](#device-capability-detection)
  - [App Availability Checking](#app-availability-checking)
- [Configuration Management](#configuration-management)
  - [Application-Level Configuration](#application-level-configuration)
  - [Merchant-Level Configuration](#merchant-level-configuration)
  - [Configuration Resolution Hierarchy](#configuration-resolution-hierarchy)
  - [Credential Management](#credential-management)
- [Payment Processing Flows](#payment-processing-flows)
  - [Payment Initiation](#payment-initiation)
  - [Authentication and Authorization](#authentication-and-authorization)
  - [Transaction Processing](#transaction-processing)
  - [Completion and Confirmation](#completion-and-confirmation)
- [Business Logic and Validation](#business-logic-and-validation)
  - [Payment Method Availability Business Rules](#payment-method-availability-business-rules)
  - [Payment Lifecycle Rules](#payment-lifecycle-rules)
  - [Validation Patterns](#validation-patterns)
  - [Error Handling and Recovery](#error-handling-and-recovery)
- [System Integrations](#system-integrations)
  - [Cards System Integration](#cards-system-integration)
  - [Bag and Checkout Integration](#bag-and-checkout-integration)
  - [Event System Integration](#event-system-integration)
- [Implementation Details](#implementation-details)
  - [Provider Selection Algorithms](#provider-selection-algorithms)
  - [Payment Method Support Detection](#payment-method-support-detection)
  - [Partial Payment Coordination](#partial-payment-coordination)
  - [Error Categorization and Handling](#error-categorization-and-handling)
- [Cross-System Integration Patterns](#cross-system-integration-patterns)
- [Implementation Limitations and Future Enhancements](#implementation-limitations-and-future-enhancements)

## Overview

The Perkd Payments Management System is a multi-provider payment processing platform that enables secure payment transactions throughout the application. The system supports digital wallets (Apple Pay, Google Pay), traditional card payments, regional payment methods (Alipay, GrabPay, LinePay, TouchnGo), stored value systems, and manual payment processing.

**Key Architectural Principles**:
- **Provider Abstraction**: Standardized interface enabling seamless provider switching and multi-provider support
- **Method Categorization**: Sophisticated classification system governing payment behavior and availability
- **Configuration Hierarchy**: Multi-level configuration resolution supporting global, regional, and merchant-specific customization
- **Device-Aware Processing**: Real-time capability detection and app availability checking

**Integration Scope**: Deep integration with commerce/bag system, offers system for voucher payments, cards system for merchant configuration, and rewards system for stored value methods.

## System Architecture

### Core Components

The payment system follows a layered architecture with sophisticated business logic governing payment method availability, provider selection, and transaction processing:

```mermaid
graph TB
    subgraph "Payment System Architecture"
        direction TB

        subgraph "Presentation Layer"
            PC[Payment Controller]
            PW[Payment Widget]
            PS[Payment Screens]
        end

        subgraph "Business Logic Layer"
            PM[Payment Manager]
            PV[Payment Validator]
            PA[Payment Actions]
            MC[Method Categorizer]
            DC[Device Capability Detector]
            AC[App Availability Checker]
        end

        subgraph "Provider Abstraction Layer"
            PI[Provider Interface]
            SM[Stripe Manager]
            MM[MyPay Manager]
            AM[Airwallex Manager]
            RM[Razer Manager]
            GM[GKash Manager]
            PP[PerkdPay Manager]
            SV[StoredValue Provider]
        end

        subgraph "Method Implementation Layer"
            AP[Apple Pay]
            GP[Google Pay]
            CP[Card Payments]
            AL[Alipay]
            GR[GrabPay]
            LP[LinePay]
            TG[TouchnGo]
            ST[Stored Value]
            CR[Crypto]
            WL[Wallet]
            MN[Manual]
            VC[Voucher]
        end

        subgraph "Configuration Layer"
            CR[Config Resolver]
            CC[Credential Cache]
            MC[Method Config]
        end
    end

    PC --> PM
    PW --> PA
    PS --> PV

    PM --> MC
    PM --> DC
    PM --> AC
    PA --> PI
    PV --> PI

    PI --> SM
    PI --> MM
    PI --> AM
    PI --> RM
    PI --> GM
    PI --> PP
    PI --> SV

    SM --> AP
    SM --> GP
    SM --> CP
    SM --> AL
    SM --> GR
    MM --> AP
    MM --> GP
    MM --> LP
    AM --> AP
    AM --> GP
    AM --> TG
    RM --> AP
    RM --> GP
    RM --> TG
    GM --> AP
    GM --> GP
    GM --> TG
    PP --> MN
    SV --> ST
    SV --> CR
    SV --> WL

    MC --> CR
    DC --> CR
    AC --> CR

    style PC fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style PM fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style PI fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style MC fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style CR fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
```

#### Core Payment Service (`src/lib/common/payments.js`)
The central orchestrator that serves as the main entry point for all payment operations:

- **Main Interface**: Exports the primary `Payments` object with key functions:
  - `Payments.pay()`: Executes payment transactions with provider validation
  - `Payments.payParam()`: Prepares payment parameters with method-provider compatibility checking
  - `Payments.setup()`: Configures payment methods with error handling and installation updates
  - `Payments.getProvider()`: Manages provider selection with stored value special handling
- **Method Categorization**: Defines critical business logic constants:
  - `DIRECT_METHODS`: `[APPLEPAY, GOOGLEPAY, CARD, STOREDVALUE, CRYPTO, WALLET]` - Single-step payment methods
  - `PART_METHODS`: `[STOREDVALUE, CRYPTO, WALLET, VOUCHER]` - Methods supporting partial payments
  - `STORED_VALUES`: `[STOREDVALUE, CRYPTO, WALLET]` - Balance-based payment methods
  - `SYNC_METHODS`: `[]` - Currently empty, reserved for synchronous payment methods
- **Provider Coordination**: Registration and coordination of payment providers with fallback logic
- **Error Handling**: Centralized error processing with `Payments.error()` and categorized user messaging

#### Method Management System (`src/lib/common/payments.js`)
Comprehensive payment method lifecycle management with sophisticated business logic:

- **Method Discovery**: `Methods.get(type, provider)` retrieves stored payment methods with filtering
- **Method Storage**: `Methods.add()` and `Methods.update()` handle persistent credential storage with event emission
- **Configuration Resolution**: `Methods.getConfig(method, countries, provider, fallback)` implements hierarchical resolution:
  1. Provider-specific configuration if provider specified
  2. Country-specific configuration matching user location
  3. Global configuration (no country restrictions)
  4. Fallback configuration if enabled
- **Credential Integration**: Secure storage via `Credentials.get/set()` with person-scoped isolation

#### PaymentList Class (`src/lib/common/payments.js`)
Sophisticated container for managing multi-method payment scenarios:

- **Balance Management**: Automatic calculation of remaining balance after partial payments
- **Method Prioritization**: Sorts by viewing card context and single-use methods first
- **Amount Coordination**: `refreshAmount()` handles complex partial payment amount distribution
- **Payment Selection**: `select()` method implements intelligent payment combination logic

#### UI Layer Components

**Payment Controller** (`src/controllers/Payment.js`)
- **Purpose**: Manages payment method selection and configuration UI
- **Functionality**: Payment widget management, method addition/removal with callbacks
- **Integration**: Interfaces with core Payment Service for method listing and processing

**Pay Controller** (`src/controllers/Pay.js`)
- **Purpose**: Manages payment confirmation and authentication UI
- **Functionality**: Biometric authentication handling with configurable timing
- **Security**: Handles secure payment confirmation flows with platform-specific authentication

#### Payment Actions (`src/lib/common/actions/pay.js`)
High-level payment operation orchestration with sophisticated business logic:

- **Request Processing**: `Actions.request()` handles payment request validation with card validity checking
- **Method Selection**: `Actions.select()` implements complex method selection logic:
  - Single available direct method auto-selection
  - Voucher method prioritization
  - Supported method filtering and sorting
- **Validation Logic**: Comprehensive validation including card validity periods and amount limits
- **Transaction Execution**: Coordinates between UI, validation, and provider systems

### Payment Providers

The system supports multiple payment providers through a standardized interface with sophisticated provider selection and fallback logic:

#### Stripe (`src/lib/common/payments/providers/stripe.js`)
- **Primary Markets**: Singapore, Hong Kong, Macau, Malaysia
- **Supported Methods**: Apple Pay, Google Pay, Alipay, GrabPay, Card Payments
- **Implementation Features**:
  - 3D Secure authentication with `REQUIRES_ACTION` and `REQUIRES_CONFIRM` handling
  - Payment intent confirmation with client secret management
  - Native pay integration with environment-specific configuration (TEST/PRODUCTION)
  - Return URL handling for app-to-app payment methods (`perkd://safepay/`, `perkd://grabpay/`)
- **Configuration**: Publishable keys, Stripe accounts, merchant identifiers

#### MyPay (`src/lib/common/payments/providers/mypay.js`)
- **Primary Markets**: Taiwan
- **Supported Methods**: Apple Pay, Google Pay, LinePay
- **Implementation Features**:
  - Gateway tokenization with `GATEWAY_TOKEN` type
  - Provider-specific merchant identifier handling
  - Payment data structure transformation for regional compliance
- **Configuration**: Gateway merchant IDs, tokenization parameters

#### Airwallex (`src/lib/common/payments/providers/airwallex.js`)
- **Supported Methods**: Apple Pay, Google Pay, TouchnGo
- **Implementation Features**:
  - Merchant identifier injection (`merchant.airwallex.me.perkd`)
  - Payment data structure handling for international processing
  - Basic parameter passing for TouchnGo integration
- **Configuration**: Merchant identifiers, payment data handling

#### Razer (`src/lib/common/payments/providers/razer.js`)
- **Primary Markets**: Malaysia
- **Supported Methods**: Apple Pay, Google Pay, TouchnGo
- **Implementation Features**:
  - Gateway tokenization with `molpay` gateway specification
  - Merchant identifier injection (`merchant.razer.me.perkd`)
  - Regional payment method specialization for Malaysian market
- **Configuration**: Gateway merchant IDs, merchant identifiers

#### GKash (`src/lib/common/payments/providers/gkash.js`)
- **Supported Methods**: Apple Pay, Google Pay, TouchnGo
- **Implementation Features**:
  - Gateway tokenization with `gkash` gateway specification
  - Merchant identifier injection (`merchant.gkash.me.perkd`)
  - Default parameter handling for unsupported methods
- **Configuration**: Gateway merchant IDs, merchant identifiers

#### PerkdPay (`src/lib/common/payments/providers/perkdpay.js`)
- **Internal Payment System**: Perkd's proprietary payment solution
- **Implementation Features**:
  - Staff ID and intent parameter handling
  - Offer ID integration for promotional transactions
  - Basic parameter passing without complex processing
- **Configuration**: Staff IDs, offer references, intent management

#### StoredValue Provider (`src/lib/common/payments/providers/storedvalue.js`)
- **Purpose**: Unified provider for all stored value payment methods
- **Implementation Features**:
  - Basic parameter passing for stored value transactions
  - Standardized interface for STOREDVALUE, CRYPTO, and WALLET methods
  - Simple parameter forwarding without provider-specific processing
- **Configuration**: Basic provider identification and parameter handling
- **Implementation Reality**: Minimal processing with direct parameter forwarding only

### Payment Methods

Each payment method is implemented as a self-contained module with standardized interfaces:

#### Digital Wallets

**Apple Pay** (`src/lib/common/payments/applepay.js`)
- **Platform**: iOS devices with Touch ID, Face ID, or Apple Watch
- **Implementation**: Uses `PaymentRequest` from `react-native-payments` and `Stripe` from `tipsi-stripe`
- **Authentication**: Native Apple Pay integration with biometric authentication through Apple's secure element
- **Features**:
  - Native Apple Pay integration with merchant validation via `merchantIdentifier`
  - Support for multiple networks through `supportedNetworks` configuration
  - Stripe provider integration for payment processing
  - Automatic payment completion with configurable timing (750ms for Stripe)
- **Configuration**: Merchant identifier, supported networks, country/currency codes
- **Security**: Tokenized payments, no card data stored on device, secure element integration

**Google Pay** (`src/lib/common/payments/googlepay.js`)
- **Platform**: Android devices with Google Play Services (detected via `DEVICE.hasGoogleService()`)
- **Implementation**: Uses `PaymentRequest` from `react-native-payments` with provider-specific tokenization
- **Authentication**: Device authentication (PIN, pattern, biometric) with Google Pay app integration
- **Features**:
  - Native Google Pay integration with multiple provider support
  - Gateway tokenization for secure payment processing
  - Support for various payment networks and authentication methods
  - Environment-specific configuration (TEST/PRODUCTION)
- **Configuration**: Gateway merchant ID, supported networks, environment settings, tokenization parameters
- **Security**: Tokenized payments, encrypted payment data, gateway-based token management

#### Traditional Payment Methods

**Card Payments** (`src/lib/common/payments/card.js`)
- **Support**: Visa, Mastercard, American Express, UnionPay
- **Implementation**: `Card` object manages comprehensive card payment operations
- **Features**:
  - Card storage and retrieval via `Card.activeCards()` with provider-specific filtering
  - Card setup through `Card.setup()` for new payment method registration
  - Payment processing via `Card.pay()` with authentication flow management
  - Support for 3D Secure authentication with `REQUIRES_ACTION` and `REQUIRES_CONFIRM` handling
  - Payment intent management with provider-specific processing
- **Authentication**: 3D Secure, biometric confirmation, payment intent confirmation
- **Security**: PCI-compliant processing, encrypted card storage, tokenized transactions
- **Validation**: Real-time card number validation, expiry checking, network detection

#### Regional Payment Methods

**Alipay** (`src/lib/common/payments/alipay.js`)
- **Provider**: Stripe integration
- **Flow**: App-to-app redirect with return URL handling
- **Authentication**: Alipay app authentication
- **Markets**: Global with focus on Chinese users

**GrabPay** (`src/lib/common/payments/grabpay.js`)
- **Provider**: Stripe integration
- **Markets**: Singapore, Malaysia
- **Flow**: App-to-app redirect with return URL handling
- **Authentication**: Grab app authentication

**LinePay** (`src/lib/common/payments/linepay.js`)
- **Provider**: MyPay integration
- **Markets**: Taiwan
- **Flow**: Web-based payment with app callback
- **Authentication**: Line app authentication

**TouchnGo** (`src/lib/common/payments/tng.js`)
- **Provider**: Razer integration
- **Markets**: Malaysia
- **Flow**: App-to-app redirect
- **Authentication**: TouchnGo app authentication

#### Stored Value Systems

**Stored Value** (`src/lib/common/payments/storedvalue.js`)
- **Purpose**: Gift cards, prepaid balances, loyalty credits
- **Current Implementation**: Basic stored value interface with simple balance retrieval
- **Features**:
  - Basic balance checking via `storedValue?.balance || 0`
  - Partial payment support with automatic amount calculation
  - Biometric authentication integration for secure access
  - Simple parameter passing for stored value transactions
- **Authentication**: Biometric confirmation required for balance access
- **Integration**: Card-based stored value management with CardMaster relationship
- **Implementation Limitations**:
  - No sophisticated balance deduction or transaction recording
  - Setup functionality marked as TODO (`TODO: buy gift card?`)
  - Provider-agnostic implementation with basic parameter forwarding

**Crypto** (`src/lib/common/payments/crypto.js`)
- **Current Implementation**: Identical to stored value implementation (shared codebase)
- **Actual Features**:
  - Basic balance retrieval via `storedValue?.balance || 0`
  - Simple parameter passing for `cardId` and `provider`
  - Placeholder `setup()` method with TODO comment for gift card functionality
- **Limitations**: No blockchain-specific logic, cryptocurrency features, or wallet integration
- **Status**: Placeholder implementation awaiting future development

**Wallet** (`src/lib/common/payments/wallet.js`)
- **Current Implementation**: Identical to stored value implementation (shared codebase)
- **Actual Features**:
  - Basic balance retrieval via `storedValue?.balance || 0`
  - Simple parameter passing for `cardId` and `provider`
  - Placeholder `setup()` method with TODO comment
- **Limitations**: No cross-card transfers, balance synchronization, or CardMaster integration
- **Status**: Placeholder implementation awaiting future development

#### Special Payment Methods

**Manual** (`src/lib/common/payments/manual.js`)
- **Current Implementation**:
  - Basic authorization checking via `authorized()` method
  - Merchant ID and card master ID validation logic
  - Action validation through `Actions.validate()`
  - TODO comment for error dialog implementation
- **Actual Features**: Simple merchant/card master authorization without sophisticated staff verification
- **Limitations**: No QR code scanning or complex staff authentication systems

**Voucher** (`src/lib/common/payments/voucher.js`)
- **Current Implementation**:
  - Integration with offers system for voucher processing
  - Basic parameter passing for voucher transactions
  - Minimal processing with direct parameter forwarding
- **Business Logic**: Relies on offers system for voucher validation and redemption
- **Integration**: Processed through offers system rather than payment system directly

## Payment Method Categorization and Business Logic

### Method Classification System

The payment system implements a sophisticated categorization system that governs payment behavior, availability, and processing logic:

```javascript
// Core categorization constants from src/lib/common/payments.js
const STORED_VALUES = [STOREDVALUE, CRYPTO, WALLET];
const DIRECT_METHODS = [APPLEPAY, GOOGLEPAY, CARD, ...STORED_VALUES];
const PART_METHODS = [...STORED_VALUES, VOUCHER];
const SYNC_METHODS = [];  // Reserved for future synchronous payment methods
```

#### Business Logic Behind Categories

**DIRECT_METHODS**: Single-step payment methods that complete transactions immediately
- **Apple Pay, Google Pay, Card**: Native payment processing without intermediate steps
- **Stored Values (StoredValue, Crypto, Wallet)**: Balance-based payments with immediate deduction
- **Business Rule**: These methods bypass multi-step confirmation flows and process directly

**PART_METHODS**: Payment methods supporting partial payment scenarios
- **Stored Values**: Can be combined with other methods when balance insufficient
- **Voucher**: Applied as partial payment with remaining balance covered by other methods
- **Business Rule**: System automatically calculates remaining balance and enables method combination

**STORED_VALUES**: Balance-based payment methods requiring balance verification
- **Common Interface**: All stored value methods share identical implementation patterns
- **Balance Checking**: System verifies available balance before payment processing
- **Business Rule**: Insufficient balance triggers partial payment flow or method selection

### Device Capability Detection

The system implements sophisticated device capability detection to determine payment method availability:

```javascript
// Device capability checking from src/lib/common/payments.js
supported: async (methods) => {
    return Payments.apps().then(supported =>     // 3rd party payment apps
        Promise.all([
            (IOS || DEVICE.hasGoogleService()) ? StripeModule.canMakeNativePayPayments() : false,
            card.supported(),
            // storedvalue.supported(),  // Currently commented out
        ]).then(([supportNative, supportCard, supportStoredValue]) => {
            if (supportNative) supported.unshift(IOS ? APPLEPAY : GOOGLEPAY);
            if (supportCard) supported.push(CARD);
            // if (supportStoredValue) supported.push(STOREDVALUE);

            return methods ? methods.filter(m => supported.includes(m)) : supported;
        }));
},
```

#### Capability Detection Logic

**Native Pay Support**:
- **iOS**: Apple Pay availability through `StripeModule.canMakeNativePayPayments()`
- **Android**: Google Pay availability via Google Services detection and native pay capability
- **Business Rule**: Native methods only available when device hardware and software support confirmed

**Card Support**: Universal availability with provider-specific validation
- **Implementation**: `card.supported()` checks provider configuration and device compatibility
- **Business Rule**: Card payments serve as universal fallback when other methods unavailable

**App Availability**: Third-party payment app detection
- **Implementation**: `Payments.apps()` scans for installed payment applications
- **Business Rule**: Regional payment methods only available when corresponding apps installed

### App Availability Checking

The system performs real-time checking for third-party payment applications:

```javascript
// App availability detection logic
apps: () => {
    return Promise.all([
        // Regional payment app detection
        Apps.installed('com.grab.grabpay'),
        Apps.installed('com.alipay.android'),
        Apps.installed('com.linecorp.linepay'),
        Apps.installed('com.touchngo.ewallet'),
    ]).then(results => {
        const availableApps = [];
        if (results[0]) availableApps.push(GRABPAY);
        if (results[1]) availableApps.push(ALIPAY);
        if (results[2]) availableApps.push(LINEPAY);
        if (results[3]) availableApps.push(TOUCHNGO);
        return availableApps;
    });
}
```

#### App Detection Business Rules

**Installation Verification**: Real-time checking prevents offering unavailable payment methods
**Regional Optimization**: Only relevant regional methods checked based on user location
**Performance Optimization**: App checking cached to prevent repeated device scanning
**User Experience**: Unavailable methods automatically filtered from payment selection UI

## Configuration Management

### Application-Level Configuration

The payment system uses a hierarchical configuration system that enables flexible payment method availability based on geographic regions, device capabilities, and business requirements.

#### Global Payment Configuration (`src/lib/Settings.json`)

```javascript
{
  "Payments": {
    "payee": "Perkd",
    "config": {
      "card": [{
        "provider": "stripe",
        "countries": ["SG", "HK", "MO", "MY"],
        "supportedNetworks": ["visa", "mastercard", "amex", "unionpay"],
        "auth": true
      }],
      "applepay": [{
        "provider": "stripe",
        "countries": ["SG", "HK"]
      }, {
        "provider": "mypay",
        "countries": ["TW"]
      }]
    },
    "methods": {
      "applepay": {
        "provider": "stripe",
        "countryCode": "SG",
        "currencyCode": "SGD",
        "merchantIdentifier": "merchant.me.perkd",
        "supportedNetworks": ["visa", "mastercard", "amex", "chinaunionpay"],
        "allowedAuthMethods": ["panOnly", "cryptogram3ds"],
        "version": "5.0.1"
      }
    }
  }
}
```

### Configuration Resolution Hierarchy

The system implements sophisticated hierarchical configuration resolution with multiple fallback levels:

```javascript
// Actual implementation from src/lib/common/payments.js
getConfig: (method, currency, provider) => {
    const { config } = Settings.get('payments'),
        { [method]: methodConfig = [] } = config || {},
        configs = methodConfig.filter(c => !provider || c.provider === provider);

    return configs.find(c => !currency || !c.currencies || c.currencies.includes(currency)) || configs[0];
},
```

#### Resolution Algorithm

**Step 1: Provider Filtering**
- If provider specified, filter configurations to matching provider only
- If no provider specified, consider all available provider configurations
- **Business Rule**: Provider-specific configurations take precedence over generic configurations

**Step 2: Currency Matching**
- Find configuration supporting the transaction currency
- If currency not specified in config, treat as universal (supports all currencies)
- **Business Rule**: Currency-specific configurations override universal configurations

**Step 3: Fallback Selection**
- If no currency match found, return first available configuration for the method
- If no configurations exist for method, return undefined (method unavailable)
- **Business Rule**: System gracefully degrades to available configurations rather than failing

#### Configuration Resolution Logic

The system uses intelligent configuration resolution that considers:

1. **Provider Specificity**: Provider-specific configurations override generic configurations
2. **Currency Compatibility**: Configurations filtered by supported currencies
3. **Geographic Restrictions**: Payment methods filtered based on user location and supported countries
4. **Device Capabilities**: Native payment methods only available on compatible devices
5. **Merchant Requirements**: Individual merchants can restrict or customize payment methods

#### Configuration Priority Order

1. **Provider + Currency Specific**: Exact match for provider and currency
2. **Provider Specific**: Provider match with universal currency support
3. **Currency Specific**: Currency match with any provider
4. **Universal Fallback**: First available configuration regardless of provider/currency
5. **Method Unavailable**: No configuration found, method not offered

### Merchant-Level Configuration

Each merchant (CardMaster) can customize payment settings through the `payments` configuration object:

#### Payment Configuration Structure

```javascript
{
  "payee": "Merchant Name",
  "currency": {
    "code": "SGD",
    "symbol": "S$",
    "precision": 2
  },
  "limits": {
    "min": 10,
    "max": 1000
  },
  "methods": {
    "applepay": {
      "provider": "stripe",
      "countryCode": "SG",
      "currencyCode": "SGD",
      "merchantIdentifier": "merchant.me.perkd",
      "supportedNetworks": ["visa", "mastercard"],
      "version": "5.0.1"
    }
  },
  "taxes": [{
    "title": "GST",
    "rate": 0.07
  }]
}
```

#### Configuration Inheritance

Merchant configurations inherit from application-level defaults with the ability to override specific settings:

1. **Base Configuration**: Application-level settings provide defaults
2. **Merchant Override**: Merchants can customize payee, currency, limits, and method-specific settings
3. **Runtime Resolution**: Final configuration is resolved at payment time based on context

### Credential Management

The payment system implements secure credential management with person-scoped storage and sophisticated method lifecycle management:

```javascript
// Actual implementation from src/lib/common/payments.js
export const Methods = {
    get: async (type, provider) => {
        const methods = await Credentials.get(_.Person.id, 'methods'),
            result = provider
                ? (methods || {})[provider] || []
                : Object.keys(methods || {}).reduce((list, pvd) => [ ...list, ...methods[pvd] ], []);
        return result.filter(m => (type ? m.type === type : true));
    },

    add: (method, source) => {
        const { type, provider, country, brand, funding, expiry, last4 } = method;
        $.Event.emit(EVENT.Payment.methodAdded, { type, provider, country, details: { brand, funding, expiry, last4 }, source });

        return Methods.get(type, provider).then(res => {
            const methods = res || [];
            methods.push(method);
            return Credentials.set(_.Person.id, 'methods', { [provider]: methods });
        });
    },
};
```

#### Credential Storage Implementation

**Person-Scoped Storage**: All payment methods stored under individual person ID
- **Implementation**: `Credentials.get/set(_.Person.id, 'methods')`
- **Isolation**: Each person's payment methods completely isolated
- **Security**: Person-level access control prevents cross-user data access

**Provider-Organized Structure**: Methods organized by payment provider
- **Structure**: `{ [provider]: [method1, method2, ...] }`
- **Benefits**: Enables provider-specific method retrieval and management
- **Flexibility**: Supports multiple methods per provider per person

**Method Lifecycle Events**: Automatic event emission for method changes
- **Event**: `EVENT.Payment.methodAdded` with method details and source tracking
- **Integration**: Enables UI updates and analytics tracking
- **Audit Trail**: Source tracking for method addition context

#### Credential Security Patterns

**Minimal Data Storage**: Only essential payment method metadata stored
- **Stored Fields**: `type`, `provider`, `country`, `brand`, `funding`, `expiry`, `last4`
- **Excluded**: Full card numbers, CVV, sensitive authentication data
- **Compliance**: Reduces PCI DSS scope by avoiding sensitive data storage

**Provider Tokenization**: Actual payment credentials managed by providers
- **Implementation**: System stores provider tokens, not raw payment data
- **Security**: Provider-managed tokenization ensures secure credential handling
- **Compliance**: Delegates sensitive data handling to PCI-compliant providers

#### Security Measures

1. **Keychain Integration**: All sensitive credentials stored in device secure keychain
2. **Encryption**: Additional encryption layer for payment method data
3. **Access Control**: Scoped access based on user, merchant, and application context
4. **Credential Rotation**: Support for credential updates and rotation
5. **Secure Transmission**: All credential exchanges use encrypted channels

## Payment Processing Flows

### Payment Initiation

The payment process begins with user interaction and follows a structured flow that ensures security, validation, and optimal user experience.

#### Payment Request Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Payment UI
    participant PM as Payment Manager
    participant PV as Payment Validator
    participant PP as Payment Provider
    participant API as Backend API

    U->>UI: Initiate Payment
    UI->>PM: Create Payment Request
    PM->>PV: Validate Request
    PV->>PV: Check Card Validity
    PV->>PV: Validate Amount Limits
    PV->>PV: Verify Merchant Config
    PV-->>PM: Validation Result

    alt Validation Success
        PM->>PM: Select Payment Methods
        PM->>UI: Display Payment Options
        U->>UI: Select Payment Method
        UI->>PM: Process Payment Selection
        PM->>PP: Prepare Payment Parameters
        PP-->>PM: Payment Parameters
        PM->>API: Execute Payment
        API-->>PM: Payment Result
        PM->>UI: Update Payment Status
        UI->>U: Show Completion
    else Validation Failure
        PV-->>UI: Display Error
        UI->>U: Show Error Message
    end

    style U fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style PM fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style API fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
```

#### Payment Request Validation

The system performs comprehensive validation before processing any payment:

1. **Request Structure Validation**
   - Required fields presence (amount, currency, merchant ID)
   - Data type and format validation
   - Request integrity verification

2. **Business Rule Validation**
   - Card validity and expiration checking
   - Amount limits enforcement (min/max thresholds)
   - Merchant authorization verification
   - Payment method availability confirmation

3. **Security Validation**
   - User authentication status
   - Merchant credential verification
   - Fraud detection screening
   - Rate limiting enforcement

### Authentication and Authorization

#### Biometric Authentication Flow

The payment system integrates biometric authentication for enhanced security:

```javascript
// Biometric authentication implementation
class Pay {
  auth(method, master) {
    return new Promise((resolve, reject) =>
      (method ? method(master) : Biometrics.auth())
        .then(res => setTimeout(() => resolve(res), BIO_WAIT))
        .catch(reject)
    );
  }
}
```

#### Authentication and Authorization Business Logic

The payment system implements nuanced authentication and authorization rules that balance security requirements with user experience considerations. These rules determine when and how authentication is required based on multiple contextual factors.

#### Biometric Authentication Decision Matrix

The system employs sophisticated logic to determine when biometric authentication is required versus optional:

**Configuration-Driven Authentication Requirements**
- Each payment method configuration includes an `auth` flag that determines baseline authentication requirements
- User preferences (`_.Preference.get().payments.biometric`) can override method-specific authentication settings
- Authentication requirements can be strengthened but never weakened by user preferences
- System-wide biometric availability influences authentication method selection

**Payment Method-Specific Authentication Logic**

**Digital Wallets (Apple Pay/Google Pay)**
- Native platform authentication is always required and handled by the respective platform
- Platform-specific biometric authentication (Touch ID, Face ID, fingerprint) is enforced by the wallet provider
- No additional application-level authentication is required due to platform security guarantees
- Authentication timing is managed by the platform with configurable delays for user feedback

**Card Payments**
- Biometric authentication is conditionally required based on configuration: `auth: auth && biometric`
- 3D Secure authentication may be required for certain transactions based on card issuer policies
- Payment intent confirmation may require additional authentication for high-risk transactions
- Authentication requirements may escalate based on transaction amount and risk assessment

**Stored Value Methods**
- Biometric authentication is mandatory for all stored value access: `auth: auth && biometric`
- Balance verification requires authentication to prevent unauthorized access to stored funds
- Authentication is required for both balance checking and payment processing operations
- Multiple stored value sources may require separate authentication for each access

**Manual Payments**
- QR code scanning serves as the primary authentication mechanism
- Staff authorization validation ensures only authorized personnel can process manual payments
- Action validation confirms the scanned QR code contains valid payment authorization data
- Merchant and staff ID verification prevents unauthorized manual payment processing

**Regional Payment Methods**
- App-specific authentication is delegated to the respective payment applications
- Return URL validation ensures authentication responses come from legitimate sources
- App-to-app authentication flows require validation of callback parameters
- Authentication state is maintained across app transitions for seamless user experience

#### Risk-Based Authentication Triggers

The system implements dynamic authentication requirements based on risk assessment:

**Transaction Amount Thresholds**
- Higher transaction amounts may trigger additional authentication requirements
- Biometric authentication may be required for transactions above certain thresholds
- Multiple authentication factors may be required for high-value transactions
- Authentication escalation follows configurable business rules based on amount ranges

**User Behavior Analysis**
- Unusual transaction patterns may trigger enhanced authentication requirements
- Geographic anomalies (transactions from unusual locations) may require additional verification
- Velocity-based triggers activate when transaction frequency exceeds normal patterns
- Device fingerprinting influences authentication requirements for new or suspicious devices

**Merchant-Specific Authentication Policies**
- Individual merchants can enforce stricter authentication requirements
- High-risk merchant categories may require enhanced authentication for all transactions
- Merchant-specific authentication policies override default system requirements
- Authentication requirements can be customized based on merchant business needs

#### Provider-Specific Authentication Requirements

Different payment providers impose varying authentication requirements:

**Stripe Provider Authentication**
- 3D Secure authentication is enforced for transactions requiring additional verification
- Payment intent confirmation may require user authentication for certain card types
- Strong Customer Authentication (SCA) compliance triggers additional authentication steps
- Provider-specific authentication flows are integrated seamlessly into the payment process

**Regional Provider Authentication**
- MyPay (Taiwan) implements region-specific authentication requirements
- Razer (Malaysia) may have different authentication standards for local compliance
- Provider-specific authentication methods are abstracted through the provider interface
- Authentication requirements vary based on local regulatory and business requirements

#### Authentication Flow Management

The system manages complex authentication flows with sophisticated state management:

**Multi-Step Authentication Processes**
- Authentication may involve multiple steps (biometric + PIN, biometric + 3D Secure)
- Authentication state is maintained throughout the payment process
- Failed authentication triggers appropriate retry or fallback mechanisms
- Authentication timeout handling ensures security while maintaining user experience

**Authentication Result Processing**
- Authentication success enables payment processing to continue
- Authentication failure triggers appropriate error handling and user guidance
- Authentication cancellation is handled gracefully without disrupting the user experience
- Authentication errors are categorized and handled with specific recovery flows

#### Authorization Validation Logic

Beyond authentication, the system implements comprehensive authorization validation:

**Staff Authorization for Manual Payments**
- Staff ID validation ensures only authorized personnel can process payments
- Action validation confirms the authorization scope matches the requested operation
- Merchant ID validation ensures staff authorization is valid for the specific merchant
- Authorization expiration and revocation are checked in real-time

**Merchant Authorization Validation**
- Merchant-specific authorization rules determine payment processing capabilities
- Authorization scope validation ensures operations are within permitted boundaries
- Cross-merchant authorization prevents unauthorized access to other merchant systems
- Authorization inheritance follows the merchant hierarchy and relationship structure

**User Authorization Context**
- User authentication status influences available payment methods and limits
- Authorization level determines transaction limits and authentication requirements
- User role and permissions affect payment processing capabilities
- Authorization context is maintained throughout the payment session for consistency

### Transaction Processing

#### Payment Method Processing Patterns

**Direct Payment Methods** (Apple Pay, Google Pay, Card)
1. Payment parameter preparation
2. Provider-specific payment request creation
3. Native payment UI presentation
4. Payment confirmation and tokenization
5. Backend transaction processing
6. Result handling and UI updates

**App-to-App Payment Methods** (Alipay, GrabPay, LinePay)
1. Payment intent creation
2. App redirect with payment parameters
3. External app authentication and confirmation
4. Return URL callback handling
5. Payment status verification
6. Transaction completion

**Stored Value Methods** (Stored Value, Crypto, Wallet)
1. Basic balance retrieval via `storedValue?.balance || 0`
2. Simple parameter passing for `cardId` and `provider`
3. Minimal processing with direct parameter forwarding
4. No sophisticated balance deduction or transaction recording
5. No balance update or synchronization mechanisms

#### Partial Payment Support

The system supports sophisticated partial payment scenarios:

```javascript
class Payments extends Array {
  select(payment) {
    if (this.balance) {
      const amount = Math.min(this.balance, payment.amount);
      Object.assign(payment, { amount });
      this.push(payment);
      this.refreshAmount();
    }
  }

  get balance() {
    return Math.max(0, this.#totalPrice - this.amount);
  }
}
```

**Partial Payment Rules**:
- Stored value methods can be combined with other payment methods
- Vouchers can provide partial discounts
- Multiple stored value sources can be combined
- Final payment method must cover remaining balance

### Completion and Confirmation

#### Transaction Completion Flow

1. **Payment Confirmation**: Provider confirms successful payment processing
2. **Backend Verification**: Server-side verification of payment status
3. **Order Fulfillment**: Order status update and fulfillment initiation
4. **User Notification**: Success confirmation and receipt generation
5. **Data Synchronization**: Local and remote data synchronization
6. **Analytics Tracking**: Payment event logging and analytics

#### Error Recovery and Retry Logic

The system implements basic error recovery with limited sophistication:

**Apple Pay Error Handling**:
```javascript
// Actual implementation from src/lib/common/payments/applepay.js
.catch(err => {
    if (err.message === ERROR_ABORT) throw CANCEL;
    throw err;
});
```

**Current Error Handling Capabilities**:
- **Apple Pay Cancellation**: Specific handling for user cancellation (`ERROR_ABORT` → `CANCEL`)
- **Basic Error Propagation**: Most errors passed through without sophisticated handling
- **Limited Recovery**: No automatic retry mechanisms or exponential backoff
- **No Provider Fallback**: No automatic fallback to alternative providers
- **Basic Timeout**: Standard promise timeout without custom timeout handling

**Implementation Limitations**:
- No comprehensive error categorization system
- No automatic retry logic for network failures
- No sophisticated provider fallback mechanisms
- No partial payment failure rollback implementation

## Business Logic and Validation

### Payment Method Availability Business Rules

The payment system employs sophisticated business logic to determine which payment methods are available to users at any given time. This availability is governed by multiple interconnected factors that work together to ensure optimal user experience while maintaining security and compliance.

#### Geographic and Regional Restrictions

Payment method availability is fundamentally constrained by geographic business rules that reflect regulatory requirements, provider coverage, and merchant preferences:

**Country-Based Method Filtering**
- Payment methods are filtered based on the user's current location and supported countries defined in the application configuration
- Each payment method configuration includes a `countries` array that explicitly defines where the method is available
- The system applies a hierarchical resolution: country-specific configurations take precedence over global configurations
- When no country-specific configuration exists, the system falls back to global configurations (those without country restrictions)

**Provider Geographic Coverage**
- Different providers support different geographic regions, creating a complex matrix of availability
- For example, Stripe supports Singapore, Hong Kong, Macau, and Malaysia, while MyPay focuses on Taiwan
- The system automatically selects the appropriate provider based on the user's location and the merchant's supported regions
- Cross-border transactions may trigger different provider selection logic based on currency and regulatory requirements

#### Device Capability Assessment

The system performs real-time assessment of device capabilities to determine payment method availability:

**Platform-Specific Method Filtering**
- Apple Pay is only available on iOS devices with Touch ID, Face ID, or Apple Watch capability
- Google Pay requires Android devices with Google Play Services installed and enabled
- The system uses `DEVICE.hasGoogleService()` to verify Google Services availability before offering Google Pay
- Platform-specific methods are automatically filtered out on incompatible devices (e.g., Apple Pay on Android)

**Hardware Capability Verification**
- Native payment methods require specific hardware capabilities verified through `StripeModule.canMakeNativePayPayments()`
- Biometric authentication availability is checked through device capability assessment
- Card payment support is verified through provider-specific capability checks
- The system gracefully degrades when hardware capabilities are insufficient

#### Third-Party Application Dependencies

Many payment methods depend on external applications, creating dynamic availability based on app installation status:

**App Installation Verification**
- Regional payment methods (Alipay, GrabPay, LinePay, TouchnGo) require their respective apps to be installed
- The system uses `Apps.checkInstalled()` to verify app availability before offering these methods
- App tracking configuration determines which apps are monitored for availability
- Payment method availability updates dynamically as users install or uninstall required apps

**App Version Compatibility**
- Payment methods may specify `maxAppVersion` requirements for compatibility
- The system uses `Version.meet()` to verify version compatibility before offering methods
- Outdated app versions may result in payment method unavailability until updates are installed

#### Merchant-Level Business Rules

Individual merchants can impose additional restrictions on payment method availability:

**Method Restriction Policies**
- Merchants can explicitly disable specific payment methods through their configuration
- Payment method availability is filtered based on the merchant's `payments.methods` configuration
- Merchants can specify provider preferences that influence method availability
- Some merchants may restrict payment methods based on transaction amounts or customer types

**Wallet and Stored Value Restrictions**
- Stored value methods can be restricted using `excludeCardMasterIds` to prevent certain merchants from accessing specific wallets
- Wallet availability is determined by `_.CardMaster.findByWallets()` which applies merchant-specific restrictions
- Cross-card wallet relationships may affect availability based on merchant policies

#### User Account and Card Status Requirements

Payment method availability is contingent on user account status and card membership:

**Card Membership Validation**
- All payment operations require valid card membership verified through `_.Card.findOneByMasterId()`
- Card validity periods are enforced: cards with future start times or past end times are considered invalid
- Card status affects payment method availability: inactive or expired cards prevent payment processing
- Digital card activation status influences stored value method availability

**User Authentication Status**
- Payment method availability may depend on user authentication level
- Biometric authentication capability affects the availability of methods requiring enhanced security
- User preferences for biometric authentication can influence method presentation

**Balance and Credit Requirements**
- Stored value methods are only available when sufficient balance exists
- Credit-based payment methods verify credit limits before presentation
- Insufficient funds scenarios may trigger alternative payment method suggestions

#### Real-Time Provider Status Assessment

The system continuously monitors provider availability to ensure reliable payment processing:

**Provider Health Monitoring**
- Real-time provider status affects payment method availability
- Provider outages or maintenance windows may temporarily disable affected methods
- The system implements fallback logic to alternative providers when primary providers are unavailable

**Configuration Validation**
- Provider credentials and configuration validity is verified before offering methods
- Invalid or expired provider configurations result in method unavailability
- The system validates provider-method compatibility before presentation

#### Dynamic Method Prioritization

Beyond simple availability, the system applies business rules for method prioritization and presentation:

**User Preference Integration**
- Previously used payment methods may be prioritized in the selection interface
- User-specific method preferences influence the order of presentation
- Viewing card context affects method sorting and prioritization

**Transaction Context Considerations**
- Transaction amount may influence method availability (e.g., minimum/maximum limits)
- Transaction type (purchase, top-up, transfer) affects method filtering
- Merchant category and business type may influence method availability

### Configuration Inheritance and Resolution Business Logic

The payment system implements a sophisticated configuration inheritance model that balances global consistency with merchant-specific customization needs. This hierarchical approach ensures that payment configurations can be centrally managed while allowing for localized business requirements.

#### Configuration Hierarchy and Precedence

The configuration resolution follows a strict precedence order that determines which settings apply in any given payment scenario:

**Application-Level Defaults (Lowest Precedence)**
- Global payment configurations defined in `Settings.json` serve as the foundation for all payment operations
- These configurations establish baseline payment method support, provider relationships, and security policies
- Application-level settings ensure consistent behavior across all merchants and regions
- Default configurations include supported networks, authentication requirements, and provider credentials

**Regional Configuration Overrides**
- Country-specific configurations override application defaults for geographic compliance and optimization
- Regional settings accommodate local payment preferences, regulatory requirements, and provider availability
- Currency-specific configurations ensure proper handling of local payment methods and exchange rates
- Regional overrides enable compliance with local financial regulations and payment industry standards

**Provider-Specific Configuration**
- Provider configurations override regional settings for specific payment processing requirements
- Each provider may have unique configuration requirements for authentication, tokenization, and processing
- Provider-specific settings accommodate different API requirements, security protocols, and feature sets
- Multiple providers can support the same payment method with different configuration parameters

**Merchant-Level Customization (Highest Precedence)**
- Individual merchants can override any configuration element to meet specific business requirements
- Merchant configurations enable custom payment flows, branding, and business logic
- CardMaster-specific settings allow for unique payment experiences while maintaining system consistency
- Merchant overrides can restrict payment methods, modify limits, or customize authentication requirements

#### Configuration Resolution Algorithm

The system employs a sophisticated resolution algorithm that intelligently selects the most appropriate configuration:

**Geographic Resolution Logic**
```
1. Search for provider-specific configuration if provider is specified
2. Search for country-specific configuration matching user's location
3. Fall back to global configuration (no country restrictions)
4. Apply fallback configuration if no matches found and fallback is enabled
```

**Conflict Resolution Principles**
- More specific configurations always override general configurations
- Merchant-level settings take absolute precedence over all other configurations
- Provider-specific settings override geographic settings when both apply
- Security-related configurations cannot be weakened by lower-precedence overrides

**Dynamic Configuration Updates**
- Configuration changes propagate through the hierarchy without requiring application restarts
- Real-time configuration updates ensure immediate application of policy changes
- Configuration validation prevents invalid settings from disrupting payment operations
- Cached configurations are invalidated when upstream changes occur

#### Merchant Configuration Inheritance Patterns

Merchants inherit base configurations but can selectively override specific elements:

**Selective Override Capability**
- Merchants can override individual configuration elements without replacing entire configurations
- Partial overrides maintain inheritance for non-specified elements
- Configuration merging preserves application-level defaults for unspecified settings
- Override validation ensures merchant customizations don't violate system constraints

**Business Rule Inheritance**
- Payment limits inherit from application defaults but can be customized per merchant
- Authentication requirements can be strengthened but not weakened by merchant overrides
- Supported payment methods can be restricted but not expanded beyond application capabilities
- Currency and regional restrictions are inherited and cannot be circumvented by merchant settings

#### Provider Configuration Management

Provider configurations require special handling due to their technical complexity and security implications:

**Credential Inheritance and Isolation**
- Provider credentials are inherited from application level but can be overridden per merchant
- Credential isolation ensures merchant-specific credentials don't affect other merchants
- Secure credential storage maintains separation between application and merchant credentials
- Credential validation ensures all required parameters are present before enabling payment methods

**Provider Capability Mapping**
- Provider configurations define which payment methods each provider supports
- Capability mapping ensures only supported method-provider combinations are offered
- Provider-specific features (like 3D Secure) are configured per provider relationship
- Feature availability is validated against provider capabilities during configuration resolution

#### Configuration Validation and Consistency

The system implements comprehensive validation to ensure configuration consistency:

**Cross-Configuration Validation**
- Payment method configurations are validated against provider capabilities
- Geographic restrictions are validated against provider coverage areas
- Currency configurations are validated against provider and regional support
- Authentication requirements are validated against device and provider capabilities

**Business Rule Consistency**
- Configuration changes are validated against existing business rules
- Conflicting configurations are detected and resolved according to precedence rules
- Invalid configurations are rejected with clear error messages
- Configuration dependencies are validated to prevent incomplete setups

#### Runtime Configuration Resolution

Configuration resolution occurs dynamically at payment time to ensure current settings apply:

**Context-Aware Resolution**
- User location, device capabilities, and merchant context influence configuration selection
- Real-time provider status affects configuration availability
- Transaction context (amount, type, currency) influences configuration application
- User preferences and history may influence configuration selection

**Performance Optimization**
- Frequently accessed configurations are cached for performance
- Configuration resolution results are memoized for repeated operations
- Cache invalidation ensures configuration changes take effect immediately
- Lazy loading prevents unnecessary configuration resolution overhead

### Payment Lifecycle Rules

#### Payment Processing Business Rules

The payment system implements sophisticated business logic that governs how payments are processed, validated, and completed. These rules ensure transaction integrity while providing flexibility for various payment scenarios.

#### Amount Validation and Limit Enforcement

Payment amount validation involves multiple layers of business logic that protect both merchants and users:

**Transaction Amount Boundaries**
- Every merchant defines minimum and maximum transaction limits through their `payments.limits` configuration
- Minimum limits prevent micro-transactions that may not be economically viable for processing
- Maximum limits provide fraud protection and comply with regulatory requirements for large transactions
- Amount validation occurs before payment method selection to prevent user frustration

**Currency Precision and Validation**
- Payment amounts must conform to currency-specific precision requirements (e.g., 2 decimal places for SGD)
- Currency validation ensures payment amounts are specified in supported currencies
- Cross-currency transactions may trigger additional validation and conversion logic
- Currency mismatch between order and payment method results in transaction rejection

**Dynamic Limit Adjustment**
- Transaction limits may be adjusted based on user authentication level and payment method
- Biometric authentication may enable higher transaction limits for enhanced security
- Stored value methods may have different limits compared to external payment methods
- Time-based limits (daily, monthly) may further restrict transaction amounts

#### Partial Payment Eligibility and Combination Rules

The system supports sophisticated partial payment scenarios governed by specific business rules:

**Partial Payment Method Classification**
- Payment methods are categorized as `DIRECT_METHODS`, `PART_METHODS`, or `STORED_VALUES`
- Only methods classified as `PART_METHODS` can be used for partial payments
- Stored value methods (`STOREDVALUE`, `CRYPTO`, `WALLET`) inherently support partial payments
- Vouchers can provide partial discounts and be combined with other payment methods

**Payment Combination Logic**
- Multiple stored value sources can be combined to cover a single transaction
- The system automatically calculates optimal payment combinations to minimize user effort
- Payment combination respects method-specific restrictions and business rules
- Final payment method must cover any remaining balance after partial payments

**Balance Calculation and Management**
- The `PaymentList` class manages complex balance calculations for multi-method transactions
- Partial payment amounts are automatically calculated based on available balances
- The system ensures total payment amount never exceeds transaction amount
- Balance verification occurs in real-time to prevent insufficient fund scenarios

#### Payment Method Prioritization and Fallback

The system implements intelligent prioritization logic for optimal user experience:

**Method Selection Prioritization**
- Single available direct payment methods are automatically selected for streamlined experience
- Voucher-type payments are prioritized to the front of payment method lists
- Supported methods are sorted before unsupported methods in selection interfaces
- User viewing context influences method prioritization (e.g., currently viewed card)

**Provider Fallback Mechanisms**
- Multiple providers can support the same payment method, enabling automatic fallback
- Provider selection considers geographic location, merchant preferences, and real-time availability
- Fallback logic attempts alternative providers when primary providers fail
- Provider health monitoring influences fallback decision-making

**Graceful Degradation Strategies**
- Unsupported payment methods are gracefully hidden rather than showing error states
- Platform-specific methods are filtered based on device capabilities
- Network connectivity issues trigger offline-capable payment method prioritization
- Provider outages result in automatic method reordering to prioritize available options

#### Transaction Limits and Velocity Checking

The system implements comprehensive transaction monitoring and limiting:

**Per-Transaction Limits**
- Individual transaction limits are enforced based on merchant configuration and payment method
- Limits vary by payment method type, with stored value methods potentially having different thresholds
- Authentication level influences transaction limits, with biometric authentication enabling higher amounts
- Special transaction types (top-up, transfer) may have different limit structures

**Velocity and Frequency Controls**
- The system monitors transaction frequency to detect unusual patterns
- Daily and monthly transaction limits provide additional fraud protection
- Velocity checking considers both transaction count and total amount over time periods
- Suspicious transaction patterns may trigger additional authentication requirements

**Risk-Based Limit Adjustment**
- Transaction limits may be dynamically adjusted based on risk assessment
- User behavior patterns influence limit calculations and enforcement
- Geographic and temporal factors may affect transaction limit application
- Merchant risk profiles influence the limits applied to transactions

#### Currency Handling and Multi-Currency Support

The payment system supports complex multi-currency scenarios:

**Currency Conversion Logic**
- Transactions involving currency conversion follow specific business rules for rate application
- Conversion rates are validated for reasonableness and recency
- Currency conversion fees may be applied based on merchant and provider policies
- Cross-border transactions may trigger additional validation and compliance checks

**Multi-Currency Payment Support**
- Payment methods may support different currencies with varying capabilities
- Currency-specific payment method availability is determined by provider support
- Regional payment methods are typically restricted to specific currencies
- Currency validation ensures compatibility between payment method and transaction currency

#### Card Validity and Membership Rules

Card-based payment processing follows strict validity and membership rules:

**Temporal Validity Enforcement**
```javascript
// Card validity period checking
if ((startTime && startTime > NOW) || (endTime && endTime < NOW)) {
  return new Error(ERROR.invalidCard);
}
```

**Membership Status Validation**
- All payment operations require valid card membership verified through database lookup
- Card membership status affects payment method availability and processing capabilities
- Inactive or suspended cards prevent payment processing regardless of other factors
- Card sharing relationships may affect payment authorization and processing

**Digital Card Activation Requirements**
- Digital cards must be fully activated before payment processing is enabled
- Activation status is verified through `c.atStep === DONE` validation
- Pending acceptance status (`c.sharer && !c.when.accepted`) prevents payment processing
- Card activation state influences stored value method availability

#### Payment Method Availability Rules

1. **Geographic Restrictions**: Payment methods filtered by user location and supported countries
2. **Device Compatibility**: Native payment methods only available on compatible devices
3. **App Installation**: Third-party payment methods require app installation verification
4. **Provider Status**: Real-time provider availability checking
5. **Merchant Configuration**: Merchant-specific method restrictions

### Validation Patterns

#### Input Validation

- **Amount Validation**: Positive numbers, currency precision, limit checking
- **Currency Validation**: Supported currency codes, merchant currency matching
- **Card Number Validation**: Luhn algorithm, format validation, network detection
- **Expiry Validation**: Future date validation, format checking

#### Business Rule Validation

- **Membership Requirements**: Valid card membership for payment processing
- **Transaction Limits**: Daily, monthly, and per-transaction limits
- **Merchant Authorization**: Merchant-specific payment restrictions
- **Offer Eligibility**: Voucher and discount validation

### Security Policies

#### Data Protection

1. **PCI Compliance**: No card data storage, tokenization for all card transactions
2. **Encryption**: All sensitive data encrypted in transit and at rest
3. **Access Control**: Role-based access to payment functions
4. **Audit Logging**: Comprehensive transaction and access logging
5. **Data Minimization**: Only necessary payment data collected and processed

#### Fraud Prevention

- **Device Fingerprinting**: Device identification for fraud detection
- **Behavioral Analysis**: Transaction pattern analysis
- **Velocity Checking**: Transaction frequency and amount monitoring
- **Geographic Validation**: Location-based transaction verification
- **Biometric Verification**: Enhanced authentication for high-value transactions

### Limits and Thresholds

#### Transaction Limits

```javascript
// Merchant-specific limits configuration
{
  "limits": {
    "min": 10,      // Minimum transaction amount
    "max": 1000     // Maximum transaction amount
  }
}
```

#### Performance Thresholds

- **Payment Processing**: Target 3-second completion for digital wallets
- **Authentication**: Biometric authentication within 2 seconds
- **Network Timeout**: 30-second timeout for provider communications
- **Retry Limits**: Maximum 3 retry attempts for failed transactions
- **Cache Duration**: Payment method cache valid for 5 minutes

## System Integrations

### Cards System Integration

The payment system is deeply integrated with the Perkd cards system, providing seamless payment experiences within the loyalty card context.

#### Card-Payment Relationship

```mermaid
graph LR
    subgraph "Cards System Integration"
        direction LR

        subgraph "Card Management"
            CM[Card Master]
            CD[Card Data]
            CC[Card Config]
        end

        subgraph "Payment Integration"
            PC[Payment Config]
            PM[Payment Methods]
            PT[Payment Transactions]
        end

        subgraph "User Experience"
            CW[Card Wallet]
            PW[Payment Widget]
            CO[Checkout Flow]
        end
    end

    CM --> PC
    CD --> PM
    CC --> PT

    PC --> CW
    PM --> PW
    PT --> CO

    style CM fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style PC fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    style CW fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
```

#### Integration Points

**CardMaster Payment Configuration**
- Each CardMaster contains payment configuration including supported methods, currency, and limits
- Payment methods are dynamically resolved based on CardMaster settings
- Merchant-specific payment provider credentials are managed per CardMaster

**Card-Scoped Payments**
- Payments are always executed within the context of a specific card
- Card validity and membership status affect payment availability
- Card-specific stored value balances are integrated into payment options

**Payment History Integration**
- Payment transactions are linked to card usage history
- Transaction data flows into card analytics and reporting
- Payment events trigger card-level notifications and updates

### Bag and Checkout Integration

The payment system provides comprehensive integration with the shopping bag and checkout processes.

#### Checkout Flow Integration

```javascript
// Bag checkout integration
const { payments } = await Actions.select({
  ...order,
  options,
  methods,
  currency,
  partial,
  getPayParam
}, master, data.methods);

// Payment execution within checkout context
function pay(payment) {
  return CardService.pay({
    masterId,
    personId,
    order,
    payments: payment,
    options,
    idempotencyKey
  });
}
```

#### Order Management Integration

**Order Creation and Validation**
- Payment system validates order data including items, pricing, and taxes
- Order totals are calculated including merchant-specific tax configurations
- Currency validation ensures consistency between order and payment currencies

**Fulfillment Integration**
- Payment completion triggers order fulfillment processes
- Payment status updates are synchronized with order status
- Failed payments result in order cancellation or retry flows

**Inventory Management**
- Payment authorization can reserve inventory items
- Payment completion confirms inventory allocation
- Payment failures release reserved inventory

### Event System Integration

The payment system leverages Perkd's event-driven architecture for loose coupling and real-time updates.

#### Payment Events

```javascript
// Payment event definitions
const EVENT = {
  Payment: {
    callback: "payment.callback",
    methodAdded: "payment.method.added",
    methodRemoved: "payment.method.removed"
  }
};

// Event emission examples
$.Event.emit(EVENT.Payment.methodAdded, {
  type,
  provider,
  country,
  details: { brand, funding, expiry, last4 },
  source
});
```

#### Event-Driven Updates

**Real-Time UI Updates**
- Payment method changes trigger immediate UI refreshes
- Payment status updates are broadcast to all interested components
- Error states are communicated through event system

**Cross-System Notifications**
- Payment completion events trigger card balance updates
- Payment method additions update user preference systems
- Payment failures trigger analytics and monitoring systems

**Analytics Integration**
- All payment events are automatically tracked for analytics
- Payment performance metrics are collected through event system
- User behavior analysis includes payment interaction patterns

## Error Handling and Recovery

### Error Classification and Handling

The payment system implements comprehensive error handling with user-friendly messaging and automatic recovery mechanisms.

#### Error Categories

**Network Errors**
- Connection timeouts and network unavailability
- Provider API failures and service outages
- Retry logic with exponential backoff
- Graceful degradation to offline modes

**Authentication Errors**
- Biometric authentication failures
- Provider authentication rejections
- Token expiration and refresh handling
- Re-authentication flow initiation

**Validation Errors**
- Invalid payment amounts or currencies
- Unsupported payment methods
- Card validity and expiration issues
- Clear user feedback and correction guidance

**Provider Errors**
- Payment processing failures
- Insufficient funds or credit limits
- Provider-specific error code translation
- Alternative payment method suggestions

#### Error Handling and Recovery Business Logic

The payment system implements sophisticated error handling and recovery mechanisms that prioritize user experience while maintaining transaction integrity. The business logic governing error handling decisions ensures appropriate responses to different failure scenarios.

#### Error Classification and Decision Trees

The system categorizes errors into distinct types, each with specific handling and recovery strategies:

**Cancellation vs. Failure Distinction**
- User-initiated cancellations (`ERROR.canceled`) are handled gracefully without error logging
- System failures trigger comprehensive error logging and recovery mechanisms
- Cancellation detection prevents unnecessary error reporting and user confusion
- Graceful cancellation handling maintains user trust and system reliability

**Recoverable vs. Non-Recoverable Errors**
- Network timeouts and connectivity issues are classified as recoverable with retry logic
- Authentication failures are recoverable through re-authentication flows
- Invalid configuration errors are non-recoverable and require system intervention
- Provider-specific errors are evaluated for recoverability based on error codes

#### Retry vs. Fail Decision Logic

The system employs intelligent decision-making to determine when to retry operations versus failing immediately:

**Network and Connectivity Errors**
- Temporary network failures trigger automatic retry with exponential backoff
- Connection timeouts are retried up to a maximum number of attempts
- Provider API failures are evaluated for retry eligibility based on HTTP status codes
- Persistent connectivity issues eventually fail with user-friendly error messages

**Authentication and Authorization Failures**
- Biometric authentication failures prompt immediate re-authentication opportunities
- Token expiration triggers automatic token refresh before retrying the operation
- Provider authentication rejections may trigger fallback authentication methods
- Repeated authentication failures eventually fail with guidance for resolution

**Provider-Specific Error Handling**
- Provider outages trigger automatic fallback to alternative providers when available
- Provider-specific error codes are translated to standardized error categories
- Temporary provider issues trigger retry logic with provider-specific backoff strategies
- Permanent provider failures result in method unavailability with user notification

#### Provider Fallback Selection Criteria

When primary providers fail, the system applies sophisticated criteria for fallback selection:

**Provider Capability Matching**
- Fallback providers must support the same payment method as the failed provider
- Geographic coverage requirements must be met by the fallback provider
- Currency support compatibility is verified before fallback provider selection
- Feature parity (3D Secure, tokenization) is considered in fallback selection

**Provider Priority and Reliability**
- Provider reliability scores influence fallback selection order
- Historical performance data affects provider prioritization
- Real-time provider health monitoring influences fallback decisions
- Merchant-specific provider preferences are considered in fallback selection

**Business Rule Compliance**
- Fallback providers must comply with the same regulatory requirements
- Merchant agreements and restrictions are validated for fallback providers
- Cost considerations may influence fallback provider selection
- Service level agreements are maintained through appropriate fallback selection

#### User-Facing Error Message Selection Logic

The system implements sophisticated logic for selecting appropriate user-facing error messages:

**Error Code Translation and Localization**
```javascript
// Error message selection logic
const code = err?.code
  ? String(err.code).startsWith('payment_') ? err.code : `payment_${err.code}`
  : '';
const message = codes.includes(code)
  ? $.L(code)  // Localized error message
  : $.L('app.error');  // Generic fallback message
```

**Context-Aware Error Messaging**
- Error messages are tailored based on the payment method that failed
- Transaction context influences error message selection and guidance
- User authentication level affects the detail level of error messages
- Previous error history may influence message selection to avoid repetition

**Actionable Error Guidance**
- Error messages include specific guidance for user resolution when possible
- Alternative payment method suggestions are provided for method-specific failures
- Contact information is provided for errors requiring support intervention
- Step-by-step recovery instructions are included for complex error scenarios

#### Recovery Flow Determination

The system determines appropriate recovery flows based on error type and context:

**Automatic Recovery Flows**
- Network failures trigger background retry with user progress indication
- Token refresh operations occur transparently without user intervention
- Provider fallback happens automatically with minimal user disruption
- Configuration reload occurs automatically when configuration errors are detected

**User-Guided Recovery Flows**
- Authentication failures prompt user re-authentication with clear instructions
- Payment method failures suggest alternative methods with explanation
- Insufficient funds errors provide guidance for adding funds or using alternative methods
- Card-specific errors provide guidance for contacting card issuers or updating information

**Escalated Recovery Flows**
- Persistent failures trigger escalation to support channels
- Complex errors result in detailed error reporting for technical investigation
- System-wide issues trigger maintenance mode with user notification
- Critical failures may require manual intervention with appropriate user communication

#### Error Context Preservation and Analysis

The system maintains comprehensive error context for analysis and improvement:

**Error Tracking and Categorization**
- All payment errors are logged with full context for analysis
- Error patterns are tracked to identify systemic issues
- Provider-specific error rates are monitored for performance assessment
- User impact assessment helps prioritize error resolution efforts

**Error Recovery Success Tracking**
- Recovery attempt success rates are tracked for optimization
- User behavior following errors is analyzed for experience improvement
- Provider fallback effectiveness is measured and optimized
- Error message effectiveness is evaluated based on user response patterns

#### Business Continuity and Graceful Degradation

The system implements business continuity measures for various failure scenarios:

**Partial System Failures**
- Individual payment method failures don't affect other methods
- Provider outages trigger graceful degradation to available providers
- Feature failures (biometric authentication) trigger fallback to alternative authentication
- Network issues trigger offline-capable payment method prioritization

**Graceful User Experience Degradation**
- Complex payment flows simplify automatically when components fail
- Optional features are disabled gracefully when underlying services fail
- User interface adapts to available functionality without breaking
- Performance degradation is communicated transparently to users

### Monitoring and Alerting

**Real-Time Monitoring**
- Payment success/failure rates tracked in real-time
- Provider performance monitoring and alerting
- Transaction volume and velocity monitoring
- Fraud detection and prevention alerts

**Error Tracking and Analysis**
- Comprehensive error logging with context
- Error pattern analysis and trending
- Provider-specific error rate monitoring
- User impact assessment and prioritization

## Performance Considerations

### Optimization Strategies

#### Payment Method Discovery

**Caching Strategy**
- Payment method availability cached for 5 minutes
- Device capability detection cached per session
- Provider status cached with TTL-based invalidation
- Configuration changes trigger cache invalidation

**Lazy Loading**
- Payment method implementations loaded on-demand
- Provider modules loaded only when required
- Payment UI components loaded just-in-time
- Background preloading for frequently used methods

#### Transaction Processing

**Parallel Processing**
- Multiple payment method validation in parallel
- Concurrent provider availability checking
- Asynchronous credential retrieval
- Background payment method setup

**Connection Pooling**
- Persistent connections to payment providers
- Connection reuse for multiple transactions
- Optimized SSL/TLS handshake management
- Provider-specific connection optimization

### Scalability Considerations

**Load Distribution**
- Provider load balancing for high-volume merchants
- Geographic provider selection for optimal performance
- Transaction queuing for peak load management
- Graceful degradation under high load

**Resource Management**
- Memory-efficient payment method storage
- Efficient credential caching and rotation
- Background cleanup of expired payment data
- Optimized database queries for payment history

### Performance Metrics

**Target Performance Benchmarks**
- Payment method discovery: < 500ms
- Digital wallet payments: < 3 seconds end-to-end
- Card payment authorization: < 5 seconds
- Stored value transactions: < 1 second
- Error recovery: < 2 seconds for retry attempts

**Monitoring and Alerting**
- Real-time performance metric collection
- SLA monitoring and alerting
- Performance regression detection
- User experience impact assessment

## Implementation Details

### Provider Selection Algorithms

The payment system implements sophisticated provider selection logic with fallback mechanisms:

```javascript
// Actual provider selection from src/lib/common/payments.js
getProvider: (method, provider) => {
    if (STORED_VALUES.includes(method)) return storedvalue;

    const { [method]: providers = [] } = PROVIDERS;
    return provider ? providers.find(p => p.name === provider) : providers[0];
},
```

#### Provider Selection Logic

**Stored Value Special Handling**:
- All stored value methods (`STOREDVALUE`, `CRYPTO`, `WALLET`) automatically route to `storedvalue` provider
- No provider selection logic for stored value methods
- Unified provider interface for all balance-based payment methods

**Standard Provider Selection**:
- If provider specified, find exact provider match from available providers
- If no provider specified, return first available provider (default selection)
- No sophisticated fallback or load balancing logic

**Provider Availability**: Basic provider lookup without health checking or availability validation

### Payment Method Support Detection

The system implements real-time payment method support detection:

```javascript
// Actual support detection from src/lib/common/payments.js
supported: async (methods) => {
    return Payments.apps().then(supported =>     // 3rd party payment apps
        Promise.all([
            (IOS || DEVICE.hasGoogleService()) ? StripeModule.canMakeNativePayPayments() : false,
            card.supported(),
            // storedvalue.supported(),  // Currently commented out
        ]).then(([supportNative, supportCard, supportStoredValue]) => {
            if (supportNative) supported.unshift(IOS ? APPLEPAY : GOOGLEPAY);
            if (supportCard) supported.push(CARD);
            // if (supportStoredValue) supported.push(STOREDVALUE);

            return methods ? methods.filter(m => supported.includes(m)) : supported;
        }));
},
```

#### Support Detection Implementation

**Native Pay Detection**:
- **iOS**: Apple Pay support via `StripeModule.canMakeNativePayPayments()`
- **Android**: Google Pay support via Google Services detection + native pay capability
- **Platform Check**: iOS detection via `IOS` flag, Android via `DEVICE.hasGoogleService()`

**Card Support**: Universal card support via `card.supported()` method
**App Support**: Third-party app detection via `Payments.apps()` method
**Stored Value Support**: Currently commented out, not actively detected

### Partial Payment Coordination

The system implements sophisticated partial payment coordination through the `PaymentList` class:

```javascript
// Actual implementation from src/lib/common/payments.js
class PaymentList extends Array {
    #totalPrice;

    get balance() {
        return Math.max(0, this.#totalPrice - this.amount);
    }

    get amount() {
        return this.reduce((total, payment) => total + payment.amount, 0);
    }

    async select(payment) {
        if (this.balance) {
            const amount = Math.min(this.balance, payment.amount);
            Object.assign(payment, { amount });

            this.push(payment);
            this.sort((a, b) => (a.singleUse ? b.singleUse ? 0 : -1 : b.singleUse ? 1 : 0));
            this.refreshAmount();
        }
    }

    refreshAmount() {
        this.forEach((payment, index) => {
            const remaining = this.#totalPrice - this.slice(0, index).reduce((sum, p) => sum + p.amount, 0);
            payment.amount = Math.min(payment.amount, remaining);
        });
    }
}
```

#### Partial Payment Business Logic

**Balance Calculation**: Automatic calculation of remaining balance after each payment selection
**Amount Limitation**: Payment amounts automatically capped at remaining balance
**Single-Use Prioritization**: Single-use payment methods (vouchers) sorted first in payment list
**Amount Redistribution**: `refreshAmount()` ensures no payment exceeds remaining balance

#### Payment Selection Rules

**Partial Payment Eligibility**: Only methods in `PART_METHODS` can be used for partial payments
**Amount Coordination**: System automatically calculates optimal amount for each payment method
**Method Combination**: Multiple partial payment methods can be combined in single transaction
**Balance Validation**: System ensures total payments don't exceed transaction amount

### Payment Processing Implementation

Payment processing is orchestrated by the `Payments.pay()` method with provider-specific handling:

#### Processing Flow

1. **Validation Phase**: Validate method and provider availability with comprehensive error checking
2. **Parameter Preparation**: Retrieve provider-specific request parameters via `payParam()` methods
3. **Method Execution**: Call method-specific `pay()` function with prepared parameters
4. **Result Handling**: Process payment result and return structured response to calling system
5. **State Management**: Update payment state and trigger appropriate event notifications

#### Method-Specific Processing Patterns

**Card Payments**
- Display authentication UI with biometric or PIN confirmation
- Handle 3D Secure authentication flows with `REQUIRES_ACTION` states
- Process payment through provider with intent confirmation
- Manage payment completion with result verification

**Digital Wallets**
- Launch native payment UI (Apple Pay/Google Pay)
- Handle platform-specific authentication and tokenization
- Process tokenized payment data through provider
- Manage payment completion with platform-specific timing

**Stored Value Methods**
- Basic balance retrieval via `storedValue?.balance || 0`
- Simple parameter passing for `cardId` and `provider`
- No sophisticated balance deduction or transaction recording
- No balance state updates or cross-system synchronization

### Platform-Specific Implementation

The system handles platform-specific behavior to ensure optimal user experience:

#### iOS Platform
- **Apple Pay Integration**: Uses `react-native-payments` and `tipsi-stripe` for native integration
- **Biometric Authentication**: Touch ID or Face ID integration via secure enclave
- **Payment Completion**: Platform-specific timing and completion handling
- **Security**: Leverages iOS secure element for payment tokenization

#### Android Platform
- **Google Pay Integration**: Uses `react-native-payments` with Google Services detection
- **Service Detection**: Validates Google Services availability via `DEVICE.hasGoogleService()`
- **Biometric Authentication**: Fingerprint or Face Unlock integration
- **Payment Processing**: Android-specific payment request handling and tokenization

### Error Handling Implementation

Comprehensive error handling ensures robust payment processing:

#### Error Classification
- **Standard Error Codes**: Defined in `common/Errors.json` with consistent error format
- **Provider Translation**: Provider-specific errors translated to standard codes
- **User Messaging**: User-friendly error messages displayed via Dialog system
- **Debug Logging**: Detailed error logging for development and debugging

#### Common Error Scenarios
- **Authentication Failures**: Biometric, 3D Secure, and provider authentication issues
- **Insufficient Funds**: Balance and credit limit validation
- **Network Issues**: Connectivity problems and timeout handling
- **Provider Unavailability**: Service outages and fallback mechanisms

## Implementation Limitations and Future Enhancements

### Current Implementation Limitations

#### Stored Value Payment Methods

**Crypto Payment Method**:
- **Current State**: Placeholder implementation identical to stored value
- **Limitations**: No blockchain integration, cryptocurrency support, or wallet management
- **TODO Items**: Gift card functionality placeholder in `setup()` method
- **Future Enhancement**: Requires complete reimplementation for actual cryptocurrency support

**Wallet Payment Method**:
- **Current State**: Placeholder implementation identical to stored value
- **Limitations**: No cross-card transfers, balance synchronization, or CardMaster integration
- **TODO Items**: Gift card functionality placeholder in `setup()` method
- **Future Enhancement**: Requires sophisticated wallet management system implementation

**Stored Value Method**:
- **Current State**: Basic balance retrieval and parameter passing
- **Limitations**: No provider-specific processing or sophisticated balance management
- **TODO Items**: Gift card purchase functionality in `setup()` method
- **Future Enhancement**: Requires comprehensive stored value processing system

#### Manual Payment Processing

**Manual Payment Method**:
- **Current State**: Basic merchant/card master ID authorization checking
- **Limitations**: No QR code scanning, sophisticated staff verification, or complex authentication
- **TODO Items**: Error dialog implementation noted in code comments
- **Future Enhancement**: Requires comprehensive staff authentication and authorization system

#### Error Handling and Recovery

**Error Processing**:
- **Current State**: Basic error propagation with minimal categorization
- **Limitations**: No automatic retry mechanisms, exponential backoff, or sophisticated recovery
- **Missing Features**: Provider fallback, network failure handling, partial payment rollback
- **Future Enhancement**: Requires comprehensive error handling and recovery system

#### Async Payment Management

**Async Payment Status**:
- **Current State**: Basic async detection via `isDirect()` and `isSync()` methods
- **Limitations**: No webhook processing, real-time status monitoring, or automatic synchronization
- **Missing Features**: Payment status updates, external provider synchronization
- **Future Enhancement**: Requires webhook infrastructure and status management system

### Placeholder Implementations

#### Methods with TODO Comments

**Stored Value Setup**: All stored value methods contain `setup: () => Promise.resolve(), // TODO: buy gift card?`
**Manual Error Handling**: Manual payment contains `// TODO: @wilson, show error dialog`
**Stored Value Support Detection**: Support detection commented out: `// storedvalue.supported()`

#### Incomplete Features

**Provider Health Monitoring**: No provider availability or health checking
**Sophisticated Provider Selection**: No load balancing or intelligent provider selection
**Comprehensive Error Categorization**: No standardized error classification system
**Payment Method Lifecycle**: No sophisticated method lifecycle management

## Cross-System Integration Patterns

### Commerce/Bag System Integration

The payment system integrates with the commerce/bag system through standardized interfaces:

*For business context and architectural rationale, see [Commerce System Documentation](./commerce.md#payment-integration)*
*For detailed bag/checkout implementation patterns, see [Bag/Checkout System Documentation](./bags.md#payment-system-integration)*

#### Payment Method Selection Integration

**Context-Aware Method Filtering**:
- Payment methods filtered based on bag contents, total amount, and merchant configuration
- Currency-based method availability determined by bag currency and payment configuration
- Geographic restrictions applied based on user location and fulfillment requirements

**Checkout Flow Coordination**:
- Payment system provides available methods to bag controller for user selection
- Selected payment method validated against bag requirements before processing
- Payment processing coordinated with offer reservation and inventory allocation

#### Three-Phase Commit Integration

**Payment Processing Coordination**:
```javascript
// Integration pattern from bag controller
const isAsync = !(Payments.isDirect(paymentMethod) || Payments.isSync(paymentMethod));
if (!isAsync && paymentResult.success) {
    _.Offer.removeReserved(order.id);  // Cleanup for sync payments
}
```

**Async Payment Handling**:
- Basic async detection determines cleanup timing for offer reservations
- Sync payments trigger immediate offer cleanup after successful processing
- Async payments require manual status checking and cleanup coordination

### Offers System Integration

#### Voucher Payment Processing

**Voucher Method Integration**:
- Voucher payments processed through offers system rather than payment system directly
- Payment system provides interface but delegates processing to offers system
- Voucher validation, redemption, and tracking handled by offers system

**Payment-Conditional Offers**:
- Limited integration for payment-method-specific promotions
- No sophisticated payment-conditional offer logic in payment system
- Integration primarily one-way (payments → offers) rather than bidirectional

### Rewards System Integration

#### Stored Value Payment Methods

**Points and Stored Value Integration**:
- Stored value payment methods integrate with rewards system for balance retrieval
- Basic balance checking via `storedValue?.balance || 0` pattern
- No sophisticated points conversion or stored value management

**Rewards Earning Integration**:
- Payment completion triggers rewards earning through event system
- Payment method and amount information provided to rewards system
- No payment-method-specific rewards calculation in payment system

### Cards System Integration

#### CardMaster Payment Configuration

**Configuration Inheritance**:
- Payment configurations inherited from CardMaster settings
- Merchant-specific payment method restrictions and preferences
- Card-scoped payment execution with proper authorization checking

**Payment History Integration**:
- Payment transactions associated with specific cards for history tracking
- Payment method preferences stored at card level
- Payment analytics and reporting integrated with card system

### Event System Integration

#### Payment Event Coordination

**Event Emission Patterns**:
```javascript
// Method addition event from src/lib/common/payments.js
$.Event.emit(EVENT.Payment.methodAdded, {
    type, provider, country,
    details: { brand, funding, expiry, last4 },
    source
});
```

**Cross-System Event Coordination**:
- Payment events trigger updates in UI, analytics, and related systems
- Payment method changes propagated through event system
- Payment completion events coordinate with order fulfillment and rewards systems

## Developer Guidelines

### Implementation Best Practices

#### Adding a New Payment Method

Follow this standardized approach for implementing new payment methods:

1. **Create Implementation File**: Create a new file following the pattern of existing methods
2. **Implement Required Interface**: All payment methods must implement the standard interface:
   ```javascript
   export default {
     payParam: (payee, details, parameters) => {
       // Prepare payment parameters for the method
       // Return promise with payment parameters
     },
     pay: (payee, details, parameters) => {
       // Handle the complete payment flow
       // Return promise with payment result
     },
     supported: () => {
       // Check if method is available on current platform
       // Return boolean or promise resolving to boolean
     },
     setup: () => {
       // Optional: Handle method setup/configuration
       // Return promise for setup completion
     }
   }
   ```
3. **Register Method**: Add the method to `METHODS` constant in the core Payment Service
4. **Configure Categories**: Add method to appropriate lists (`DIRECT_METHODS`, `PART_METHODS`, `STORED_VALUES`) if needed
5. **Update Configuration**: Add method configuration to Settings.json if required

#### Adding a New Payment Provider

Implement new payment providers following this pattern:

1. **Create Provider File**: Create implementation following existing provider patterns
2. **Implement Provider Interface**: Implement the required provider functions:
   ```javascript
   export default {
     request: (method, payment, credential) => {
       // Create provider-specific payment request parameters
       // Validate payment configuration with method requirements
       // Return provider-specific parameters or null if invalid
     },
     parameters: (method, details, token, customerId, amount) => {
       // Construct parameters for payment processing
       // Handle method-specific parameter formatting
       // Return formatted parameters for backend processing
     }
   }
   ```
3. **Add Method-Specific Functions**: Implement method-specific functions if needed:
   ```javascript
   // Example for card payments
   card: {
     intent: (parameters) => { /* Create payment intent */ },
     pay: (parameters) => { /* Process card payment */ }
   }
   ```
4. **Register Provider**: Add provider to `PROVIDERS` constant in core Payment Service
5. **Update Method Implementations**: Update relevant payment methods to support the new provider

#### Security Implementation

1. **Data Protection**: Never store sensitive payment information (card numbers, CVV) directly
2. **Token Management**: Use provider tokens for all payment operations with secure storage
3. **Authentication**: Implement proper authentication for payment actions with biometric support
4. **Compliance**: Follow PCI compliance guidelines for handling payment data
5. **Encryption**: Ensure all payment data is encrypted in transit and at rest

#### Testing Implementation

1. **Test Environment Setup**: Use test credentials provided by payment providers
2. **Environment Configuration**: Set environment to TEST or PRODUCTION based on context
3. **Comprehensive Testing**: Test all error scenarios as well as successful payment paths
4. **Validation Testing**: Verify transaction details and amounts are processed correctly
5. **Complex Scenarios**: Test partial payments and payment combinations if applicable
6. **Platform Testing**: Test on both iOS and Android platforms with platform-specific features

#### Performance Optimization

1. **Caching Strategy**: Implement appropriate caching for payment method discovery and configuration
2. **Lazy Loading**: Load payment components and providers only when needed
3. **Connection Management**: Optimize network connections and implement proper timeout handling
4. **Memory Management**: Efficient memory usage and proper cleanup of payment resources
5. **Background Processing**: Use background processing for non-critical payment operations

### Testing Guidelines

#### Unit Testing
- Test all payment method implementations with mock providers
- Validate error handling and edge cases
- Test configuration resolution and validation logic
- Verify security measures and credential handling

#### Integration Testing
- Test provider integrations with sandbox environments
- Validate payment flows end-to-end
- Test error scenarios and recovery mechanisms
- Verify event system integration and data flow

#### Security Testing
- Penetration testing for payment flows
- Credential security validation
- Data encryption verification
- Compliance testing for PCI and regional requirements

### Maintenance and Updates

#### Provider Updates
- Regular provider SDK updates and compatibility testing
- Configuration updates for new features and requirements
- Security patch management and deployment
- Performance optimization and monitoring

#### Compliance Management
- Regular PCI compliance audits and updates
- Regional regulation compliance monitoring
- Security policy updates and implementation
- Documentation maintenance and updates

---

*This documentation provides a comprehensive overview of the Perkd Payments Management System. For specific implementation details, refer to the source code and individual component documentation. For questions or clarifications, consult the development team or system architects.*

