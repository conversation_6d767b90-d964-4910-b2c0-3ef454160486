# Offers Management System

## Table of Contents
- [Overview](#overview)
- [Offer Types and Processing](#offer-types-and-processing)
- [User Experience Journeys](#user-experience-journeys)
- [Architecture Overview](#architecture-overview)
- [Data Models and Schema](#data-models-and-schema)
- [Data Flow Architecture](#data-flow-architecture)
- [API Endpoints and Services](#api-endpoints-and-services)
- [Business Logic and State Management](#business-logic-and-state-management)
- [Integration Architecture](#integration-architecture)
- [Implementation Patterns](#implementation-patterns)

## Overview

The Offers Management System is a comprehensive promotional framework within the Perkd application that enables merchants to create, distribute, and manage digital promotional campaigns. **As a platform, Perkd needs to support the omni-channel requirements of different merchants**, requiring sophisticated architecture that handles diverse redemption patterns, business rules, and integration needs.

The system supports multiple offer types (discounts, vouchers, tickets, pickup offers) with sophisticated lifecycle management, real-time redemption tracking, and secure sharing capabilities. Built on a multi-layered architecture, it provides seamless integration with card management, payment processing, vending machine services, and user engagement systems.

### Value Proposition

**For Users:**
- Single app for all promotional offers across participating merchants
- Flexible redemption options (in-store, online, in-app, vending machines) based on convenience
- Automatic application of eligible offers during checkout
- Social sharing capabilities with friends and family
- Contactless pickup fulfillment through vending machines and kiosks

**For Merchants:**
- Unified campaign creation with multi-channel distribution
- Real-time performance analytics across all redemption channels
- Reduced integration complexity through single platform API
- Advanced targeting and personalization capabilities
- IoT integration for vending machines and automated fulfillment

**For the Platform:**
- Scalable architecture supporting diverse merchant requirements
- Comprehensive tracking and attribution across channels
- Fraud prevention and security measures
- Performance optimization for high-volume operations

### Key Capabilities

- **Multi-Channel Redemption**: Seamless redemption across in-store, online, in-app, and vending machine channels
- **Multi-Type Offer Support**: Discounts, vouchers, tickets, and pickup offers with specialized behaviors
- **Lifecycle Management**: Complete offer lifecycle from creation to expiration with state transitions
- **Real-Time State Management**: Live synchronization of offer availability and redemption status
- **Social Distribution**: User-to-user sharing with policy enforcement and tracking
- **Automated Application**: Smart application of eligible offers during checkout processes
- **Contextual Discovery**: Location and behavior-based offer recommendations
- **Machine Integration**: IoT integration with vending machines and kiosks for pickup fulfillment

### Technology Foundation

The offers system is built on the same robust technology stack as the core Perkd application:
- **Data Persistence**: Realm database with schema version 278+ for local storage
- **Synchronization**: Bidirectional sync with backend APIs for real-time updates
- **Event System**: Centralized event-driven architecture for loose coupling
- **State Management**: Sophisticated state machines for offer lifecycle management
- **Security**: Multi-layered security with encryption and validation

## Offer Types and Processing

The system supports four distinct offer types, each optimized for different promotional strategies and merchant requirements.

### Processing Decision Matrix

| Offer Type | Processing System | In Store | Online | In App | Auto-Application |
|------------|-------------------|----------|--------|--------|------------------|
| **Discount** | Redemption Engine | ✅ Barcode/QR | ✅ Manual Code | ✅ Auto-Apply | ✅ Checkout |
| **Voucher (Payment)** | Payment System | ❌ Not Supported | ❌ Not Supported | ✅ Payment Selection | ✅ Checkout |
| **Ticket** | Redemption Engine | ✅ NFC/QR (Venue) | ❌ Not Applicable | ✅ Check-in Flow | ❌ Manual |
| **Pickup** | Redemption Engine | ✅ NFC/QR (Machine) | ❌ Not Applicable | ✅ Slide-to-Redeem | ❌ Manual |

### 1. Discount Offers

**Purpose**: Standard promotional offers providing percentage or fixed-amount price reductions.

**Characteristics:**
- Processed through the redemption engine with standard authentication methods
- Support all redemption channels (in-store, online, in-app)
- Automatically applied during in-app checkout when eligible
- Can have usage limits, time constraints, and item qualifiers

**User Experience:**
- **Multi-Channel**: User selects preferred redemption method
- **In-App**: Automatic application during checkout
- **In-Store/Online**: Authentication required (QR scan, NFC, or manual code)

**Technical Identification:**
```javascript
kind === "discount"
```

### 2. Payment Vouchers

**Purpose**: Fixed-value payment instruments processed as payment methods during checkout.

**Characteristics:**
- Processed through the payment system, NOT the redemption engine
- Available only for in-app redemption during checkout
- Appear as selectable payment methods alongside credit cards
- Single-use with fixed monetary value

**User Experience:**
- Presented as offers in the user's offer list
- Selected as payment method during checkout (not redeemed as discount offers)
- Automatically deduct their value from order total
- No authentication methods required (payment method selection only)

**Technical Identification:**
```javascript
kind === "voucher" && options?.payment === true && discount?.kind === "fixed"
```

### 3. Tickets

**Purpose**: Time-bound offers for events, services, or experiences with check-in functionality.

**Characteristics:**
- Processed through redemption engine with specialized check-in features
- Optimized for NFC and QR code authentication methods
- Include venue, timing, and location-specific validation

**User Experience:**
- Check-in interface rather than traditional redemption
- Location and time-based validation
- Integration with calendar and reminder systems

**Technical Identification:**
```javascript
kind === "ticket"
```

### 4. Pickup Offers

**Purpose**: Physical item fulfillment through vending machines, kiosks, and store pickup locations with item specifications and inventory management.

**Characteristics:**
- Processed through the redemption engine with specialized machine integration
- Support vending machines, kiosks, and store pickup locations
- Support offline redemption for vending machine scenarios

**User Experience:**
- **Machine-Initiated Flow**: User initiates pickup on machine → slides to redeem in app → authentication → machine dispenses products
- **NFC Authentication**: Opens NFC interface → user taps phone on machine NFC emitter
- **QR Code Authentication**: Opens QR scanner → user scans QR code on machine
- **Standard UI**: Uses same slide-to-redeem interface as other offer types

**Data Structure:**
When `offer.kind === 'pickup'`, the offer contains:
- `orderId`: ID of the original order linked to the items being picked up
- `items`: Array containing details of all products to be picked up

**Technical Identification:**
```javascript
kind === "pickup"
```

## User Experience Journeys

### Discovery and Engagement Journey

#### How Users Find Offers
1. **Through Cards**: Opends loyalty card → sees relevant offers in integrated widgets
2. **Location Triggers**: Walking near merchants triggers contextual offers
3. **Notifications**: Push notifications for expiring offers, new arrivals, etc.
3. **Direct Browsing** (future): Users actively browse available offers through dedicated offer sections

#### How Users Engage with Offers
1. **Quick Scan**: Users scan offer list for interesting opportunities
2. **Detail Exploration**: Users tap to see full details, images, terms
3. **Action Selection**: Users choose to redeem, share, or bookmark

### Journey 1: Multi-Channel Discount

**Scenario**: User receives a 20% off offer from her favorite beauty brand.

1. **Discovery**: Opens card → sees offer widget
2. **Interest**: Taps offer - view images, description, terms, expiration
3. **Channel Choice** (only if offer supports multiple channels):
   - Sees "In Store" and "In App" options → selects "In App"
4. **Shop**: Navigates to brand shop → adds items to cart
5. **Checkout**: Discount auto-applies → completes purchase
6. **Confirmation**: Receives receipt with savings summary

**Alternative Flow (In Store)**: 
   - Step 3: Selects "In Store" (button becomes "Redeem")
   - Step 4: Slides to redeem
   - Step 5: Shows barcode / QR code for cashier to scan

**Single-Channel Offer**: When only one channel is available, users see the appropriate redemption interface directly (no channel selection step)

**Key Mechanics**: Channel flexibility, automatic discount application

### Journey 2: Payment Voucher

**Scenario**: User applies $25 voucher for in-app purchase

1. **Shop**: Adds $40 product to cart
2. **Checkout**: Sees "$25 Voucher" pre-selected as payment method
3. **Completion**: Pays $15 remainder with Apple Pay / Google Pay / Credit Card

**Key Mechanics**: Payment method integration, partial payment handling, seamless checkout flow

### Journey 3: Event Check-in

**Scenario**: User checks into concert venue with digital ticket

1. **Presentation**: Taps ticket - view venue, timing, add to calendar, send to friend
2. **Arrival**: Approaches venue entrance with NFC tags
3. **Check-in**: Taps on "Check In" button (opens NFC interface) → taps phone on NFC tag
4. **Validation**: System verifies user's location, event timing, and ticket authenticity
5. **Access**: Ticket updates to "Checked In" → enters venue

**Key Mechanics**: NFC support, real-time validation

### Journey 4: Vending Machine Integration

**Scenario**: User redeems free snack offer at smart vending machine

1. **Signup**: Scans QR on machine → downloads app → receives offer
2. **Selection**: Chooses snack from machine display
3. **Redemption & Authentication** (machine-dependent):
   - **NFC**: Slides to redeem (app opens NFC interface) → taps phone on machine NFC emitter
   - **QR Code**: Slides to redeem (app opens QR code scanner) → scans QR code on machine
4. **Completion**: Machine dispenses product → offer marked redeemed

**Key Mechanics**: IoT integration, contactless authentication

### Journey 5: Pickup Offer Redemption

**Scenario**: User picks up items from vending machine with pickup offer

1. **Machine Interaction**: Selects pickup option on machine interface
2. **Redemption & Authentication**: (machine-dependent)
   - **NFC Flow**: Slides to redeem (app opens NFC interface) → taps phone on machine NFC emitter
   - **QR Code Flow**: Slides to redeem (app opens QR code scanner) → scans QR code on machine
3. **Completion**: Machine dispenses products → offer marked redeemed

**Key Mechanics**: Standard app UI

### Journey 6: Partner App Integration

**Scenario**: User redeems Uber discount via deep link integration

1. **Redemption**: Slides to redeem → opens deep link with embedded Uber promo code
2. **Integration**: Deep link opens Uber app with promo code auto-applied
3. **Completion**: Offer status updates to "Redeemed" in Perkd app

**Key Mechanics**: Zero manual code entry

##### Journey 7: Social Transfer

**Scenario**: User transfers offer to friend

**Sender:**
1. **Transfer**: Taps share → selects contact → chooses notification method → confirms transfer
2. **Offer Status Change**:
   - Offer becomes "Transferring" after transfer confirmation
   - Once recipient accepts, offer becomes "Transferred"

**Recipient:**
1. **Receive**: Receives notification → installs / opens Perkd → accepts offer
2. **Usage**: Redeems offer, or shares the offer with others (if share policies allow)

**Key Mechanics**: Contact integration, state synchronization

### Channel Selection Logic

Users see channel options based on offer configuration:

**Single Channel Offers:**
- **In-App Only**: Direct "Auto-applied when shop in app" button
- **In-Store Only**: Direct "Redeem" button
- **Online Only**: Direct "Redeem" button

**Multi-Channel Offers:**
- **Two Buttons Displayed**:
  - "In App" (for in-app redemption)
  - "In Store" or "Online" (for traditional redemption)
- **User Selection**: Buttons transform interface based on selection
- **Context Switching**: Users can change selection before confirming redemption

## Architecture Overview

The Offers Management System follows a sophisticated multi-layered architecture that emphasizes modularity, scalability, and maintainability while providing rich user experiences and robust data management.

### System Architecture

```mermaid
graph TB
    subgraph "🎯 User Experience Layer"
        UX1[Offer Discovery<br/>Location & Context-based]
        UX2[Offer Details<br/>Rich Media & Terms]
        UX3[Redemption Flow<br/>Multi-channel Support]
        UX4[Sharing Interface<br/>Social Distribution]
    end

    subgraph "📱 Presentation Layer"
        P1[Offer Containers<br/>src/containers/Offer/]
        P2[Offer Components<br/>src/components/Offer/]
        P3[Offer Widgets<br/>Card Integration]
        P4[Navigation<br/>Modal & Stack]
    end

    subgraph "🎮 Business Logic Layer"
        B1[Offer Controller<br/>src/controllers/Offer.js]
        B2[Offer Actions<br/>src/lib/common/actions/offer.js]
        B3[Offer Services<br/>src/lib/common/services/offer.js]
        B4[State Management<br/>src/lib/offers.js]
    end

    subgraph "💾 Data Layer"
        D1[Offer Model<br/>src/lib/models/Offer.js]
        D2[Related Models<br/>Code, Discount, Redemption]
        D3[Realm Database<br/>Local Storage]
        D4[Sync Engine<br/>Bidirectional Sync]
    end

    subgraph "🌐 External Integration"
        E1[Backend APIs<br/>REST Endpoints]
        E2[Card Services<br/>Integration]
        E3[Payment Systems<br/>Transaction Processing]
        E4[Location Services<br/>Geofencing]
    end

    %% User Experience Flow
    UX1 --> UX2
    UX2 --> UX3
    UX2 --> UX4
    UX3 --> UX1

    %% Presentation Layer Connections
    UX1 --> P1
    UX2 --> P1
    UX3 --> P1
    UX4 --> P1
    P1 --> P2
    P1 --> P3
    P1 --> P4

    %% Business Logic Connections
    P1 --> B1
    P2 --> B1
    B1 --> B2
    B1 --> B3
    B2 --> B4
    B3 --> B4

    %% Data Layer Connections
    B2 --> D1
    B3 --> D1
    D1 --> D2
    D1 --> D3
    D3 --> D4

    %% External Integration
    B3 --> E1
    B1 --> E2
    B3 --> E3
    B1 --> E4
    D4 --> E1

    %% Styling with darker backgrounds and white text
    classDef ux fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef presentation fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef business fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef data fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef external fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class UX1,UX2,UX3,UX4 ux
    class P1,P2,P3,P4 presentation
    class B1,B2,B3,B4 business
    class D1,D2,D3,D4 data
    class E1,E2,E3,E4 external
```

### Architectural Principles

- **Separation of Concerns**: Clear boundaries between presentation, business logic, and data layers
- **Event-Driven Communication**: Loose coupling through centralized event system
- **State Management**: Centralized offer state management with real-time updates
- **Service Integration**: Modular integration with card, payment, and location services
- **Performance Optimization**: Multi-level caching and lazy loading strategies

### Widget-Based Architecture

The offers system leverages the Perkd widget framework to provide modular, reusable components that integrate seamlessly with the card management system:

#### Offer Widget Implementation

The Offer Widget serves as the primary entry point for offer functionality within cards, extending the DataWidget framework:

````javascript
export default class Offer extends DataWidget {
    constructor(definition, owner, data = {}, credentials) {
        super(definition, owner, data, credentials);

        // Automatic kind filtering to prevent conflicts
        const exclude = this.master.widgets
            .filter(w => w.param.model === OFFER)
            .reduce((kinds, w) => kinds.concat(w.param.kinds), []);

        if (!param.kinds && !!exclude.length) {
            const kinds = _.Offer.kinds(cardId).filter(k => !exclude.includes(k));
            Object.assign(param, { kinds: kinds.length ? kinds : [OFFER] });
        }
    }

    init() {
        super.init();
        this.Controller = Controller;
    }
}
````

#### Widget Configuration and Lifecycle

**Configuration Parameters:**
- **Model**: Specifies the data model (default: 'offer')
- **Kinds**: Defines which offer types to display (e.g., 'offer', 'ticket', 'voucher')
- **Filtering**: Automatically excludes kinds handled by other widgets on the same card

**Lifecycle Management:**
- **Constructor**: Sets up initial configuration and determines offer kinds to display
- **init()**: Initializes the widget and sets the Controller reference
- **addListeners()**: Registers for offer update and deletion events
- **removeListeners()**: Cleans up event listeners
- **onDone()**: Handles cleanup when the widget is closed, clearing view state

#### Widget Integration Patterns

- **Base Class**: Extends `DataWidget` to leverage data management capabilities
- **Child Classes**: Specialized widgets like `Ticket` extend the base Offer Widget
- **Controller Integration**: Initializes and communicates with the Offer Controller
- **Event Management**: Manages lifecycle events and updates through the Event system

## Data Models and Schema

The Offers Management System employs a sophisticated data model architecture with multiple interconnected schemas that support complex offer types, redemption patterns, and business rules.

### Core Offer Schema

The primary `Offer` model serves as the foundation for all offer types with comprehensive properties for different use cases:

**Key Properties:**
- **Identity**: `id` (primary key), `masterId` (template reference), `cardId` (association)
- **Content**: `name`, `title`, `description`, `terms`, `brand`
- **Classification**: `kind` (discount/voucher/ticket/pickup), `state`, `merchantId`
- **Timing**: `startTime`, `endTime`, `issuedAt`, `modifiedAt`
- **Redemption**: `code`, `barcode`, `barcodeType`, `redemption` object
- **Media**: `images` array, `style` configuration
- **Behavior**: `options`, `place`, `venue`, `checkin` details
- **Pickup Data**: `orderId` (order reference), `items` (product details) for pickup offers

### Related Data Models

The offer system includes several specialized models that extend core functionality:

- **OfferCode**: Manages redemption codes for different channels (store, online, in-store)
- **OfferDiscount**: Handles discount calculations, qualifiers, and entitlements
- **Redemption**: Defines redemption rules, limits, channels, and authorization methods
- **OfferImage**: Manages visual assets and presentation media
- **OfferWhen**: Tracks timing events (received, viewed, redeemed, expired)
- **OfferTouchPoint**: Defines redemption interaction points and methods

### State Model and Transitions

The offer state system provides comprehensive lifecycle management with sophisticated state transitions:

```mermaid
stateDiagram-v2
    [*] --> PENDING : Offer Created
    PENDING --> ACTIVE : Activation Time
    PENDING --> CANCELLED : Merchant Cancel
    
    ACTIVE --> REDEEMED : User Redeems
    ACTIVE --> UPCOMING : Future Start
    ACTIVE --> EXPIRED : End Time Passed
    ACTIVE --> TRANSFERRING : Share Initiated
    
    UPCOMING --> ACTIVE : Start Time Reached
    UPCOMING --> EXPIRED : End Time Passed
    UPCOMING --> CANCELLED : Merchant Cancel
    
    REDEEMED --> FULLY_REDEEMED : All Uses Exhausted
    REDEEMED --> EXPIRED : End Time Passed
    
    TRANSFERRING --> TRANSFERRED : Share Complete
    TRANSFERRING --> ACTIVE : Share Cancelled
    
    TRANSFERRED --> [*] : Final State
    FULLY_REDEEMED --> [*] : Final State
    EXPIRED --> [*] : Final State
    CANCELLED --> [*] : Final State
```

| State | Description | User Actions | System Behavior |
|-------|-------------|--------------|-----------------|
| `pending` | Created but not active | None | Hidden from user |
| `active` | Available for redemption | Redeem, Share, View | Full functionality |
| `upcoming` | Scheduled for future | View, Share | Preview mode |
| `redeemed` | Successfully used | View History | Track usage |
| `fullyredeemed` | All uses exhausted | View History | Archive state |
| `expired` | Past expiration | View History | Read-only |
| `transferred` | Shared to other user | View History | Track provenance |
| `transferring` | Share in progress | Cancel Transfer | Temporary state |
| `cancelled` | Merchant cancelled | None | Hidden from user |

### Data Relationships

The offer data model maintains complex relationships with other system entities to support comprehensive functionality:

```mermaid
erDiagram
    Offer ||--|| OfferCode : has
    Offer ||--o| OfferDiscount : contains
    Offer ||--|| Redemption : defines
    Offer ||--o{ OfferImage : displays
    Offer ||--|| OfferWhen : schedules
    Offer }|--|| Card : belongs_to
    Offer }|--|| CardMaster : issued_by
    Redemption ||--o| OfferTouchPoint : through

    Offer {
        string id PK
        string masterId FK
        string cardId FK
        string kind
        string state
        datetime startTime
        datetime endTime
    }

    OfferCode {
        string id PK
        string store
        string instore
        string online
    }

    Redemption {
        string id PK
        int limit
        int remain
        bool multiple
        string authorize
    }

    %% Styling with darker backgrounds and white text
    %%{init: {'theme':'dark'}}%%
```

**Key Relationships:**
- **Card Association**: Each offer belongs to a specific loyalty card and inherits card-level permissions
- **Master Template**: Offers are instantiated from CardMaster templates with business rules
- **Code Management**: Redemption codes support multiple channels (in-store, online, mobile)
- **Media Assets**: Rich media support with multiple image formats and sizes
- **Timing Control**: Sophisticated scheduling with start/end times and event tracking

## Data Flow Architecture

The data flow architecture ensures consistent, real-time synchronization between local storage, user interfaces, and backend services while maintaining performance and reliability.

### Data Synchronization Flow

```mermaid
graph TB
    subgraph "📱 Client-Side Data Flow"
        C1[User Actions<br/>UI Interactions]
        C2[Local State<br/>Realm Database]
        C3[Event System<br/>Real-time Updates]
        C4[UI Components<br/>React Native]
    end

    subgraph "🔄 Synchronization Layer"
        S1[Sync Engine<br/>Bidirectional]
        S2[Change Tracking<br/>Delta Updates]
        S3[Conflict Resolution<br/>Merge Strategies]
        S4[Offline Queue<br/>Deferred Operations]
    end

    subgraph "🌐 Backend Integration"
        B1[REST APIs<br/>CRUD Operations]
        B2[WebSocket<br/>Real-time Events]
        B3[Database<br/>Persistent Storage]
        B4[Business Logic<br/>Server Processing]
    end

    subgraph "🔧 Supporting Services"
        SS1[Cache Layer<br/>Performance]
        SS2[Analytics<br/>Usage Tracking]
        SS3[Notifications<br/>Push Delivery]
        SS4[Security<br/>Validation]
    end

    %% Client-Side Flow
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> C1

    %% Synchronization Flow
    C2 --> S1
    S1 --> S2
    S2 --> S3
    S3 --> S4

    %% Backend Integration
    S1 --> B1
    S1 --> B2
    B1 --> B3
    B2 --> B4

    %% Supporting Services
    S1 --> SS1
    B4 --> SS2
    B4 --> SS3
    B1 --> SS4

    %% Styling with darker backgrounds and white text
    classDef client fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef sync fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef backend fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef support fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff

    class C1,C2,C3,C4 client
    class S1,S2,S3,S4 sync
    class B1,B2,B3,B4 backend
    class SS1,SS2,SS3,SS4 support
```

### Data Flow Patterns

#### 1. Offer Discovery Data Flow
1. **Location Update**: GPS coordinates trigger geofencing checks
2. **Context Analysis**: User behavior and preferences analyzed
3. **Offer Matching**: Backend algorithms match relevant offers
4. **Local Sync**: New offers synchronized to local database
5. **UI Update**: Event system notifies UI components of new offers

#### 2. Redemption Data Flow
1. **User Initiation**: User triggers redemption action in UI
2. **Local Update**: Optimistic update to local database
3. **Backend Validation**: Server validates redemption rules and availability
4. **Confirmation**: Success/failure response updates local state
5. **Event Propagation**: UI components receive real-time updates

#### 3. Sharing Data Flow
1. **Share Request**: User initiates sharing with recipient selection
2. **Policy Validation**: System checks sharing policies and limits
3. **Transfer Processing**: Backend creates shared offer instances
4. **Notification Delivery**: Recipients receive share notifications
5. **State Synchronization**: All parties receive updated offer states

## API Endpoints and Services

The Offers Management System exposes a comprehensive REST API that supports all offer lifecycle operations with robust error handling and security measures.

### Core API Endpoints

The offer service layer provides standardized endpoints for all offer operations:

#### Offer Retrieval
```javascript
// Fetch multiple offers by IDs
GET /Offers/app/fetch?ids={offerIds}
```

#### Offer Redemption
```javascript
// Redeem an offer instance
POST /Offers/{id}/app/redeem
Body: { at: timestamp, count: number, options: object }
```

#### Offer Request/Issuance
```javascript
// Request new offer from card master
POST /CardMasters/{id}/app/offer/request
Body: { cardId: string, offerId: string, quantity: number }
```

#### Offer Sharing
```javascript
// Share offer to single recipient
POST /Offers/{id}/app/share
Body: { recipient: object, mode: string, options: object }

// Share offer to multiple recipients
POST /Offers/{id}/app/shareToMany
Body: { recipients: array, mode: string, options: object }

// Cancel pending share
POST /Offers/{id}/app/share/cancel
Body: { sharingId: string }
```

#### Notification Management
```javascript
// Mark offers as notified/viewed
POST /Offers/app/notified
Body: { idList: array, at: timestamp, view: string }
```

### Service Layer Architecture

The service layer abstracts API communication and provides consistent interfaces for offer operations:

````javascript
export default {
    fetch: ({ ids }) => call({ ...API.fetch, qs: { ids } }),

    redeem: (id, at = newDate(), count = 1, options) => call({
        ...API.redeem,
        routeParams: { id },
        body: { at, count, options },
    }),

    request: (id, cardId, offerId, quantity = 1) => call({
        ...API.request,
        routeParams: { id },
        body: { cardId, offerId, quantity },
    }),

    share: ({ id, recipient = {}, mode, options = {} }) => call({
        ...API.share,
        routeParams: { id },
        body: { recipient, mode, options },
    }),
};
````

### API Configuration and Settings

API endpoints are configured through the centralized settings system with comprehensive timeout and error handling options:

**Endpoint Configuration Features:**
- **Timeout Management**: Configurable timeouts for different operation types
- **Error Handling**: Automatic retry logic and user-friendly error messages
- **Security Headers**: Authentication tokens and request signing
- **Rate Limiting**: Built-in protection against excessive API calls

### Error Handling and Resilience

The API layer implements comprehensive error handling strategies:

#### Network Error Handling
- **Automatic Retry**: Exponential backoff for transient failures
- **Offline Queue**: Operations queued when network unavailable
- **Graceful Degradation**: Local-only operations when possible
- **User Feedback**: Clear error messages and recovery suggestions

#### Business Logic Errors
- **Validation Errors**: Input validation with specific error codes
- **Authorization Errors**: Permission and authentication failures
- **Resource Conflicts**: Concurrent modification handling
- **Rate Limiting**: Throttling and quota management

## Business Logic and State Management

The business logic layer orchestrates complex offer operations while maintaining data consistency and enforcing business rules across the entire system.

### State Management Architecture

The offer state management system provides centralized control over offer lifecycle with sophisticated state transitions and validation:

````javascript
export const Offers = {
    stateOf: (offer, now = newDate()) => {
        const { redemption, startTime, endTime, state, when } = offer || {};

        if ([TRANSFERRED, TRANSFERRING, FULLY_REDEEMED, CANCELLED].includes(state)) {
            return state;
        }

        const { redeemed, authorized } = when || {},
            { remain } = redemption || {};

        if (remain !== null && redeemed && remain < 1) return REDEEMED;
        if (authorized) return REDEEMED;
        if (startTime && (new Date(startTime) > now)) return UPCOMING;
        if (endTime && (new Date(endTime) < now)) return EXPIRED;

        return ACTIVE;
    },

    valid: (offer) => {
        const state = Offers.stateOf(offer);
        return [ACTIVE, UPCOMING].includes(state);
    },
};
````

### Business Rules Engine

The system implements a comprehensive business rules engine that governs offer behavior:

#### Redemption Rules
- **Usage Limits**: Single-use, multi-use, and unlimited redemption patterns
- **Time Constraints**: Start/end times, blackout periods, and scheduling rules
- **Location Restrictions**: Geofenced redemption areas and venue-specific offers
- **User Eligibility**: Account status, loyalty tier, and behavioral requirements

#### Sharing Rules
- **Transfer Policies**: Who can share, how many times, and to whom
- **Inheritance Rules**: How shared offers inherit or modify original properties
- **Expiration Handling**: Shared offer expiration relative to original or absolute
- **Tracking Requirements**: Audit trails and analytics for shared offers

#### Validation Logic
- **Real-Time Validation**: Instant verification of offer validity and availability
- **Conflict Resolution**: Handling concurrent redemption attempts
- **Inventory Management**: Stock tracking for limited-quantity offers
- **Fraud Prevention**: Anomaly detection and security measures

### Advanced Business Rules and Logic Patterns

The offers system implements sophisticated business logic that governs complex scenarios and edge cases beyond basic offer management.

#### Offer State Transition Business Rules

**Time-Based State Transitions:**
- **Upcoming to Active**: Offers automatically transition from `UPCOMING` to `ACTIVE` when `startTime` is reached, but only if the card is still valid
- **Active to Expired**: Offers transition to `EXPIRED` when `endTime` passes, regardless of redemption status
- **Redemption State Logic**: Offers with `when.redeemed` or `when.authorized` timestamps transition to `REDEEMED` state
- **Inventory Depletion**: Offers with `redemption.remain < 1` automatically transition to `FULLY_REDEEMED` when redemption limits are exhausted

**Card Lifecycle Integration:**
- **Card Validity Dependency**: Offers are only visible and redeemable when their associated card is active and within its validity period
- **Card Expiration Impact**: When a card expires (`endTime < NOW`), all associated offers become unavailable regardless of their individual expiration dates
- **Card Deletion Cascade**: When a card is deleted, all associated offers are marked with `deletedAt` timestamp for soft deletion
- **Active Card Filtering**: Only offers from active cards are included in unread counts and discovery algorithms

#### Redemption Eligibility and Authorization

**Multi-Level Authorization Checks:**
- **Merchant Authorization**: Redemption requires authorization through `masterIds`, `merchantIds`, or `cardMasterIds` validation
- **Channel Restrictions**: Offers can be restricted to specific redemption channels via `redemption.channels` array (e.g., "perkd", "instore")
- **Authorization Methods**: Different authorization methods (`SCAN`, `NFC`, manual) have different validation requirements
- **Location-Based Validation**: Venue-specific offers require GPS proximity validation within configured range limits

**Inventory and Concurrency Management:**
- **Optimistic Locking**: Local redemption occurs immediately with `localRedeem()` followed by server validation
- **Reservation System**: During checkout, offers are reserved via `Reserved.add()` to prevent double-redemption
- **Recovery Mechanisms**: Failed transactions trigger `recover()` to restore offer availability
- **Housekeeping Logic**: Periodic cleanup checks payment status and removes reservations for completed or cancelled orders

#### Sharing and Transfer Business Logic

**Generation Tracking and Provenance:**
- **Generation Inheritance**: Shared offers track their generation depth via `sharer.generation` to prevent infinite sharing chains
- **Origin Tracking**: Each shared offer maintains `originId` and `sharingId` for complete audit trails
- **Share Mode Enforcement**: Different sharing modes (`transfer`, `invite`) have different inheritance rules and limitations
- **Policy Validation**: Sharing policies from `card.master.sharePolicies` govern who can share to whom

**Share State Management:**
- **Transferring State**: Offers in `TRANSFERRING` state cannot be redeemed until transfer completes or is cancelled
- **Share Cancellation**: Pending shares can be cancelled via `shareCancel()` which reverts the offer to its original state
- **Recipient Validation**: Share recipients must meet eligibility criteria defined in sharing policies
- **Cross-Card Sharing**: Offers can be shared across different card types based on `toCardMasterIds` configuration

#### Payment Integration and Transaction Validation

**Order Processing Integration:**
- **Offer Reservation**: During checkout, eligible offers are reserved via `_.Offer.reserve(ids, masterId, orderId)`
- **Payment Status Monitoring**: System monitors payment intent status (`paid`, `cancelled`) to determine offer fate
- **Transaction Rollback**: Failed payments trigger `_.Offer.recover(ids)` to restore offer availability
- **Discount Application**: Offers automatically apply discounts during checkout based on item matching rules

**Item Matching and Qualification:**
- **Product Qualification**: Offers validate against specific items via `Offers.items.match(itemsToRedeem, offerItems)`
- **Order Context**: Offers can be tied to specific orders via `orderId` for targeted redemption
- **Quantity Validation**: Redemption quantity is validated against offer limits and remaining availability
- **Price Threshold**: Some offers require minimum purchase amounts for eligibility

#### Fraud Prevention and Security Measures

**Velocity and Pattern Analysis:**
- **Redemption Rate Limiting**: System tracks redemption frequency to detect unusual patterns
- **Device Correlation**: Cross-device activity analysis helps identify potential fraud
- **Scan Validation**: Barcode scanning requires multiple successful reads for high-value offers
- **Location Verification**: GPS validation ensures redemption occurs at authorized venues

**Business Rule Enforcement:**
- **Time Window Validation**: Strict enforcement of offer validity windows with server-side verification
- **Usage Limit Enforcement**: Redemption limits are enforced both locally and server-side
- **Authorization Validation**: Multiple layers of authorization checks prevent unauthorized redemption
- **Audit Trail Maintenance**: Complete tracking of all offer state changes and redemption attempts

#### Commerce Integration

**Bag/Checkout System Integration:**
- **Two-Phase Reservation**: Offers reserved during checkout to prevent double-redemption
- **Multi-Criteria Selection**: Priority, savings, and waste minimization algorithms for offer combination selection
- **Dynamic Re-optimization**: Offer savings recalculated after each discount application
- **Rollback Coordination**: Failed transactions automatically restore offer availability through `_.Offer.recover()`

*For detailed commerce integration patterns and business context, see [Commerce System Documentation](./commerce.md#offer-integration)*
*For technical implementation details of offer selection and reservation, see [Bag/Checkout System Documentation](./bags.md#transaction-coordination)*

#### Advanced Integration Patterns

**Card Master Policy Integration:**
- **Bag Policy Offers**: Special offers defined in `cardMaster.bagPolicy` for shopping cart integration
- **Fulfillment Policy**: Offers respect fulfillment policies for delivery, pickup, and in-store redemption
- **Channel-Specific Rules**: Different business rules apply based on redemption channel (mobile app, in-store, online)
- **Merchant-Specific Constraints**: Individual merchants can define custom redemption rules and restrictions

**Cross-System Validation:**
- **Real-Time Inventory**: Integration with inventory systems for stock-dependent offers
- **Payment System Coordination**: Coordination with payment processors for transaction validation
- **Location Service Integration**: GPS and geofencing validation for location-based offers
- **User Account Status**: Integration with user account status and loyalty tier validation

#### Offer Lifecycle Edge Cases and Special Scenarios

**Purging and Cleanup Logic:**
- **Automatic Purging**: Offers with `purgeTime` set are automatically removed from local storage after the specified date
- **Soft Deletion**: Offers are soft-deleted via `markDelete()` which sets `deletedAt` timestamp rather than hard deletion
- **Orphaned Offer Handling**: Offers without valid associated cards are filtered out during discovery and listing
- **Expired Offer Retention**: Expired offers are retained for historical purposes and analytics but excluded from active operations

**Multi-Use and Redemption Limit Logic:**
- **Null Remain Handling**: When `redemption.remain` is `null`, the offer has unlimited redemptions
- **Zero Remain Logic**: When `redemption.remain` reaches 0, the offer transitions to `FULLY_REDEEMED` state
- **Multiple Flag**: The `redemption.multiple` flag determines if an offer can be redeemed more than once by the same user
- **Limit Enforcement**: The `redemption.limit` defines the maximum number of redemptions across all users

**Time-Sensitive Business Rules:**
- **Grace Period Handling**: Offers may have implicit grace periods where they remain redeemable slightly past expiration
- **Timezone Considerations**: All time-based validations use server timezone to ensure consistency across regions
- **Blackout Periods**: Some offers may have blackout periods where redemption is temporarily disabled
- **Scheduled Activation**: Offers can be pre-created with future `startTime` for scheduled campaigns

**Error Recovery and Resilience:**
- **Network Failure Recovery**: Failed redemption attempts are queued via `_.Action.defer()` for retry when connectivity returns
- **State Inconsistency Resolution**: Local and server state mismatches are resolved through periodic synchronization
- **Partial Failure Handling**: If part of a multi-offer redemption fails, the system gracefully handles partial success
- **Rollback Mechanisms**: Failed transactions trigger automatic rollback of local state changes

#### Business Logic Integration Points

**Shopping Cart and Checkout Integration:**
- **Automatic Discount Application**: Eligible offers automatically apply discounts during checkout without user intervention
- **Discount Stacking Rules**: Complex rules govern how multiple offers can be combined in a single transaction
- **Item-Level Validation**: Offers validate against specific cart items based on product categories and qualifiers
- **Real-Time Price Updates**: Cart totals update in real-time as offers are applied or removed

**Loyalty Program Integration:**
- **Tier-Based Eligibility**: Offer availability may depend on user's loyalty tier or account status
- **Points Integration**: Some offers may require loyalty points in addition to or instead of monetary payment
- **Behavior-Based Targeting**: Offers are targeted based on user purchase history and behavior patterns
- **Cross-Program Benefits**: Offers may provide benefits across multiple loyalty programs or merchant partnerships

**Notification and Engagement Rules:**
- **Notification Timing**: Offers trigger notifications based on sophisticated timing algorithms considering user behavior
- **Engagement Tracking**: The system tracks when offers are viewed, shared, and redeemed for analytics
- **Reminder Logic**: Expiring offers trigger reminders based on user preferences and offer value
- **Contextual Triggers**: Location-based and time-based triggers activate relevant offer notifications

**Analytics and Business Intelligence:**
- **Redemption Analytics**: Comprehensive tracking of redemption patterns, success rates, and user engagement
- **Performance Metrics**: Offers track conversion rates, sharing velocity, and revenue impact
- **A/B Testing Support**: The system supports offer variations for testing different promotional strategies
- **Fraud Detection**: Machine learning algorithms analyze redemption patterns to detect potential fraud

These business rules ensure the offers system operates reliably across complex real-world scenarios while maintaining data integrity, user experience quality, and business objective alignment. The sophisticated rule engine handles edge cases gracefully while providing the flexibility needed for diverse promotional strategies and merchant requirements.

### Controller Architecture

The Offer Controller serves as the primary orchestrator for offer-related operations:

````javascript
async redeem(offer) {
    const { card } = this,
        { options, redemption } = offer,
        { actions, buttonLink, appOnly } = options || {},
        authorize = redemption?.authorize || options?.authorize;

    if (authorize === SCAN) return this.byScan(offer);
    if (authorize === NFC) return this.byNfc(offer);

    // Handle action-based redemption
    if (acts) {
        offer.update({ items });
        const rendered = renderObject(acts, { card, offer });
        await this.doAction(rendered);
    }
}
````

#### Controller Responsibilities
- **Operation Coordination**: Orchestrates complex multi-step operations
- **State Validation**: Ensures operations comply with business rules
- **Event Management**: Emits events for UI updates and analytics
- **Error Handling**: Provides consistent error handling across operations
- **Integration Management**: Coordinates with card, payment, and location services

#### Core Controller Methods

The Offer Controller provides a comprehensive API for offer management operations:

**Data Management Methods:**
- **refresh(data, init)**: Updates the offer list and grouped data with real-time synchronization
- **getGrouped()**: Organizes offers by status and priority for optimal user experience
- **updateTopbar(props)**: Manages navigation bar state and pagination information

**View Presentation Methods:**
- **view(callbacks, componentId, ...)**: Renders the offer view with appropriate navigation context
- **openList()**: Opens the offer list view with filtering and sorting options
- **openDetail(offer)**: Displays detailed offer information with rich media and actions

**Redemption Management Methods:**
- **redeem(offer)**: Initiates the redemption flow for an offer with validation
- **openRedeem(offer, options)**: Opens the redemption confirmation screen with context
- **byScan(offer)**: Handles QR/barcode scanning redemption flow
- **byNfc(offer)**: Manages NFC-based redemption authentication

**Sharing and Social Methods:**
- **share(offer)**: Starts the sharing process for an offer with policy validation
- **cancelShare(offer, sharingId)**: Cancels pending share operations
- **notified(offers, timestamp)**: Marks offers as viewed for analytics tracking

#### Navigation Control Patterns

The controller implements sophisticated navigation patterns optimized for mobile user experience:

**Navigation Types:**
- **List to Detail**: Seamless navigation between offer list and individual offer details
- **Modal Presentations**: Shows redemption confirmations and sharing interfaces as modals
- **Redemption Flows**: Manages complex multi-step redemption processes
- **Return Navigation**: Handles back navigation after completion with state preservation

**Navigation State Management:**
- **Deep Link Support**: Direct navigation to specific offers via URL schemes
- **State Preservation**: Maintains navigation state across app lifecycle events
- **Context Awareness**: Navigation adapts based on user context and offer state
- **Performance Optimization**: Lazy loading of navigation targets for improved performance

## Integration Architecture

The Offers Management System integrates seamlessly with multiple subsystems within the Perkd ecosystem, providing cohesive user experiences and maintaining data consistency across all touchpoints.

### Card Management Integration

The offers system is deeply integrated with the card management infrastructure:

#### Card-Offer Association
- **Automatic Discovery**: Offers automatically surface when users view associated cards
- **Lifecycle Synchronization**: Card state changes affect offer availability and presentation
- **Cross-Reference Navigation**: Seamless navigation between cards and their associated offers
- **Unified Analytics**: Combined tracking of card and offer engagement metrics

#### Data Sharing Patterns
- **Shared Models**: Card and offer models share common data structures and validation
- **Event Coordination**: Card events trigger offer updates and vice versa
- **State Consistency**: Coordinated state management ensures data integrity
- **Performance Optimization**: Shared caching strategies reduce redundant data loading

### Payment System Integration

Offers integrate with payment processing to enable automatic discount application and transaction tracking:

#### Transaction Processing
- **Automatic Application**: Eligible offers automatically apply during checkout
- **Real-Time Validation**: Payment flow validates offer eligibility and availability
- **Transaction Linking**: Offers track associated payment transactions
- **Refund Handling**: Offer redemptions properly handle payment refunds and reversals

#### Shopping Cart Integration
- **Cart-Level Discounts**: Offers can apply to entire shopping cart or specific items
- **Stacking Rules**: Complex rules govern how multiple offers interact
- **Preview Functionality**: Users see discount previews before completing purchase
- **Inventory Coordination**: Offer availability syncs with product inventory

### Location Services Integration

Location-based functionality enables contextual offer discovery and geofenced redemption:

#### Geofencing and Proximity
- **Automatic Triggers**: Location changes trigger relevant offer notifications
- **Venue-Specific Offers**: Offers tied to specific merchant locations
- **Proximity Scoring**: Distance-based offer relevance and sorting
- **Privacy Controls**: User-controlled location sharing and tracking preferences

#### Place and Venue Integration
- **Merchant Mapping**: Offers linked to specific merchant locations and venues
- **Navigation Integration**: Direct navigation to offer redemption locations
- **Hours and Availability**: Offer availability respects merchant operating hours
- **Multi-Location Support**: Chain merchants with location-specific offer variations

### Notification and Engagement Integration

The offers system leverages the notification infrastructure for user engagement:

#### Push Notification Integration
- **Contextual Triggers**: Location, time, and behavior-based offer notifications
- **Personalization**: Machine learning-driven notification targeting
- **Delivery Optimization**: Optimal timing and frequency for maximum engagement
- **Deep Link Support**: Notifications deep link directly to specific offers

#### In-App Messaging
- **Contextual Messages**: In-app messages promote relevant offers
- **Rich Media Support**: Images, videos, and interactive content in offer messages
- **Action Integration**: Direct redemption and sharing actions from messages
- **Analytics Tracking**: Comprehensive tracking of message engagement and conversion

### Reminder System Integration

The offers system integrates with the reminder infrastructure to provide proactive user engagement:

#### Expiration Reminders
- **Smart Timing**: Expiring offers trigger reminders based on user preferences and behavior patterns
- **Customizable Rules**: Custom reminder rules can be applied to different offer types
- **Multi-Channel Delivery**: Reminders delivered via push notifications, in-app messages, and email
- **User Control**: Users can customize reminder frequency and timing preferences

#### State-Based Reminders
- **Offer Lifecycle Tracking**: Reminder system tracks offer states and timelines automatically
- **Contextual Triggers**: Location-based reminders when users are near redemption venues
- **Behavioral Triggers**: Reminders based on user shopping patterns and preferences
- **Intelligent Scheduling**: Machine learning optimizes reminder timing for maximum effectiveness

## Implementation Patterns

### Widget Integration
- **Extend Base Offer Widget**: Specialized widgets inherit from base Offer Widget rather than creating standalone components
- **Controller Delegation**: Business logic delegates to Offer Controller rather than embedding in UI components
- **Event-Driven Updates**: Use event system for cross-widget communication to maintain loose coupling with card system

### Offer Management Patterns
- **State Centralization**: Use `Offers` utility methods for consistent state management and validation across components
- **Redemption Flow Consistency**: Follow established redemption patterns when implementing new authentication methods
- **Share Controller Usage**: All sharing operations must use the share controller to ensure policy compliance and audit trails

### Integration Guidelines
- **Card System Integration**: Integrate through established widget patterns rather than direct card manipulation
- **Payment System Coordination**: Follow payment integration patterns for voucher processing and discount application
- **Notification Leverage**: Use notification system hooks for offer-related user engagement and lifecycle events

### Code Organization

**File Structure:**
- Place offer-specific components in `src/components/Offer/`
- Organize offer containers in `src/containers/Offer/`
- Keep offer models in `src/lib/models/Offer/`
- Maintain offer utilities in `src/lib/offers.js`

**Naming Conventions:**
- Use descriptive names that reflect offer functionality
- Follow established naming patterns for consistency
- Use TypeScript interfaces for complex offer data structures
- Document complex business logic with clear comments

This comprehensive architecture provides a robust, scalable foundation for offer management while maintaining the flexibility needed for future enhancements and evolving business requirements. The system's modular design, comprehensive error handling, and performance optimizations ensure reliable operation at scale while delivering exceptional user experiences.
